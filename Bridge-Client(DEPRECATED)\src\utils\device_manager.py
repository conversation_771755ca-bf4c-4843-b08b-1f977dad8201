import threading
import logging
import time
import json
from datetime import datetime
from src.utils.data_formatter import format_data, is_valid_reading
from src.services.db_service import DBService

logger = logging.getLogger(__name__)


class DeviceManager:
    def __init__(self):
        self.devices = {}
        self.com_service = None
        self.socketio = None
        self.db_path = None
        self.running = True
        self.lock = threading.Lock()

    def initialize(self, com_service, socketio, db_path):
        """Initialize the device manager with required services"""
        self.com_service = com_service
        self.socketio = socketio
        self.db_path = db_path

    def restore_devices(self, devices):
        """Restore devices from database"""
        for device in devices:
            try:
                port = device.get("port")
                hioki_id = device.get("hioki_id")
                if port and hioki_id:
                    self.add_device(port, hioki_id)
                    logger.info(f"Restored device {hioki_id} on port {port}")
            except Exception as e:
                logger.error(f"Failed to restore device: {e}")

    def add_device(self, port, hioki_id):
        """Add a new device"""
        with self.lock:
            if port in self.devices:
                logger.warning(f"Device already exists on port {port}")
                return False

            try:
                device = {
                    "port": port,
                    "hioki_id": hioki_id,
                    "connected": True,
                    "last_reading": None,
                    "last_update": None,
                    "test_mode": False,
                    "thread": None,
                    "fail_count": 0,
                }
                device["thread"] = threading.Thread(
                    target=self._device_reading_loop, args=(port, hioki_id), daemon=True
                )
                device["thread"].start()
                self.devices[port] = device

                # Save to database

                DBService(self.db_path).save_device(port, hioki_id)

                # Emit device added event
                if self.socketio:
                    self.socketio.emit(
                        "device_added", {"port": port, "hioki_id": hioki_id}
                    )

                logger.info(f"Added device {hioki_id} on port {port}")
                return True
            except Exception as e:
                logger.error(f"Failed to add device: {e}")
                return False

    def remove_device(self, port):
        """Remove a device"""
        with self.lock:
            if port not in self.devices:
                logger.warning(f"No device found on port {port}")
                return None

            try:
                device = self.devices[port]
                hioki_id = device["hioki_id"]

                device["connected"] = False
                if device["thread"] and device["thread"].is_alive():
                    device["thread"].join(timeout=1.0)

                # Close the COM port
                self.com_service.close_port(port)
                del self.devices[port]

                # Remove from database, allowing fallback to hioki_id if the
                # stored port format differs
                DBService(self.db_path).remove_device(port, hioki_id)

                # Emit device removed event
                if self.socketio:
                    self.socketio.emit(
                        "device_removed", {"port": port, "hioki_id": hioki_id}
                    )

                logger.info(f"Removed device {hioki_id} from port {port}")
                return hioki_id
            except Exception as e:
                logger.error(f"Failed to remove device: {e}")
                return None

    def get_all_devices(self):
        """Get all registered devices"""
        with self.lock:
            device_list = []
            for port, device in self.devices.items():
                device_info = {
                    "port": port,
                    "hioki_id": device["hioki_id"],
                    "connected": device["connected"],
                    "last_reading": device["last_reading"],
                    "last_update": device["last_update"],
                    "test_mode": device["test_mode"],
                }
                device_list.append(device_info)
            return device_list

    def set_device_test_mode(self, port, enable):
        """Set device test mode"""
        with self.lock:
            if port not in self.devices:
                logger.warning(f"No device found on port {port}")
                return False
            try:
                self.devices[port]["test_mode"] = enable
                logger.info(f"Set test mode to {enable} for device on port {port}")
                return True
            except Exception as e:
                logger.error(f"Failed to set test mode: {e}")
                return False

    def _device_reading_loop(self, port, hioki_id):
        """Background thread for reading from a device"""
        logger.info(f"Starting reading loop for device {hioki_id} on port {port}")

        try:
            ser = self.com_service.open_serial(port)

            fail_count = 0
            timeout_limit = 5

            while (
                port in self.devices
                and self.devices[port]["connected"]
                and self.running
            ):
                try:
                    ser.write(b"READ?\r\n")
                    raw_data = ser.readline().decode("utf-8").strip()

                    if not raw_data:
                        fail_count += 1
                        if fail_count >= timeout_limit:
                            available_ports = self.com_service.get_available_ports()
                            if port not in available_ports:
                                raise IOError("COM port no longer available")
                            else:
                                logger.info(
                                    f"Device {hioki_id} on port {port} is idle"
                                )
                                fail_count = 0
                                time.sleep(1.0)
                                continue
                        time.sleep(1.0)
                        continue
                    else:
                        fail_count = 0

                    reading = format_data(raw_data)
                    timestamp = datetime.now().isoformat()

                    if reading is not None and is_valid_reading(reading):
                        self.devices[port]["last_reading"] = reading
                        self.devices[port]["last_update"] = timestamp

                        # Update database

                        DBService(self.db_path).update_reading(port, reading, timestamp)

                        # Emit reading event to dashboard (always)
                        if self.socketio:
                            self.socketio.emit(
                                "device_reading",
                                {
                                    "port": port,
                                    "hioki_id": hioki_id,
                                    "reading": reading,
                                    "timestamp": timestamp,
                                    "testing": self.devices[port]["test_mode"],
                                },
                            )

                        # Publish to MQTT only if NOT in test mode (handled by COMService)
                        if not self.devices[port]["test_mode"]:
                            self.com_service.publish_reading_mqtt(
                                hioki_id, reading, timestamp
                            )
                        else:
                            logger.info(
                                f"Test mode: Reading {reading} from {port} (not published to MQTT)"
                            )
                    else:
                        logger.warning(
                            f"Ignoring invalid reading: {reading} from {port}"
                        )

                    time.sleep(1.0)

                except Exception as e:
                    logger.error(
                        f"Error in reading loop for device {hioki_id}: {e}"
                    )

                    if port in self.devices:
                        self.devices[port]["connected"] = False

                    DBService(self.db_path).mark_offline(
                        port, datetime.now().isoformat()
                    )

                    if self.socketio:
                        self.socketio.emit(
                            "device_disconnected", {"port": port, "hioki_id": hioki_id}
                        )

                    self.com_service.close_port(port)
                    break

        except Exception as e:
            logger.error(f"Failed to start reading loop for device {hioki_id}: {e}")

            if port in self.devices:
                self.devices[port]["connected"] = False

                DBService(self.db_path).mark_offline(port, datetime.now().isoformat())

                if self.socketio:
                    self.socketio.emit(
                        "device_disconnected", {"port": port, "hioki_id": hioki_id}
                    )

            self.com_service.close_port(port)

    def shutdown_services(self):
        """Shutdown all services"""
        logger.info("Shutting down device manager...")
        self.running = False

        with self.lock:
            for port, device in list(self.devices.items()):
                try:
                    device["connected"] = False
                    if device["thread"] and device["thread"].is_alive():
                        device["thread"].join(timeout=1.0)
                except Exception as e:
                    logger.error(f"Error stopping device thread: {e}")

        # Close all COM ports
        if self.com_service:
            self.com_service.shutdown()

        logger.info("Device manager shutdown complete")
