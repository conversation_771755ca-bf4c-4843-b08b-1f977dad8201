document.addEventListener('DOMContentLoaded', () => {
    const deviceForm = document.getElementById('deviceForm');
    const portSelect = document.getElementById('portSelect');
    const hiokiNumber = document.getElementById('hiokiNumber');
    const deviceList = document.getElementById('deviceList');
    const logContainer = document.getElementById('logContainer');
    const clearLogBtn = document.getElementById('clearLog');


    // Fetch available COM ports
    async function loadComPorts() {
        try {
            const response = await fetch('/api/get_ports');
            const data = await response.json();
            const ports = data.ports || [];
    
            // Preserve the current selection
            const previousSelection = portSelect.value;
    
            portSelect.innerHTML = '<option value="">Select COM Port</option>';
            ports.forEach(port => {
                const option = document.createElement('option');
                option.value = port;
                option.textContent = port;
                // Restore selection if possible
                if (port === previousSelection) {
                    option.selected = true;
                }
                portSelect.appendChild(option);
            });
        } catch (error) {
            addLogEntry(`Error loading COM ports: ${error}`, 'error');
        }
    }
    



    // Add device
    async function addDevice(event) {
        event.preventDefault();
        const port = portSelect.value;
        const hioki_id = hiokiNumber.value;

        if (!port || !hioki_id) {
            addLogEntry('Please select a port and enter Hioki number', 'error');
            return;
        }

        try {
            const response = await fetch('/api/add_device', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ port, hioki_id })
            });
            const result = await response.json();

            if (result.status === 'success') {
                addLogEntry(`Device ${hioki_id} added on ${port}`, 'info');
                loadDevices();
                deviceForm.reset();
            } else {
                addLogEntry(`Failed to add device: ${result.message || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            addLogEntry(`Error adding device: ${error}`, 'error');
        }
    }

    // Load connected devices
    async function loadDevices() {
        try {
            const response = await fetch('/api/devices');
            const data = await response.json();
            const devices = data.devices || {};

            deviceList.innerHTML = '';

            Object.entries(devices).forEach(([port, deviceData]) => {
                deviceList.innerHTML += createDeviceCard(port, deviceData);
            });
        } catch (error) {
            addLogEntry(`Error loading devices: ${error}`, 'error');
        }
    }

    function createDeviceCard(port, deviceData) {
        const hioki_id = deviceData.hioki_id;
        const lastReading = deviceData.last_reading || 'No reading';
        const lastUpdate = deviceData.last_update || deviceData.last_reading_time || 'Never';
        const isTesting = deviceData.test_mode || false;
        const isConnected = deviceData.connected !== false;
        const testingClass = isTesting ? 'device-card-testing' : '';
        const offlineClass = !isConnected ? 'device-card-offline' : '';
        const testButtonText = isTesting ? 'Stop Test' : 'Test';
        const connectionClass = isConnected ? 'status-connected' : 'status-disconnected';
        const connectionText = isConnected ? 'Connected' : 'Disconnected';

        return `
            <div class="device-card ${testingClass} ${offlineClass}" id="device-${port.replace(/[^a-zA-Z0-9]/g, '')}">
                <div class="device-header">
                    <span class="status-indicator ${connectionClass}" title="${connectionText}"></span>
                    <h3>Hioki ${hioki_id}</h3>
                    <div class="device-buttons">
                        <button class="button button-secondary test-button" onclick="window.testReading('${port}')" ${!isConnected ? 'disabled' : ''}>${testButtonText}</button>
                        <button class="button button-danger" onclick="window.removeDevice('${port}')">Remove</button>
                    </div>
                </div>
                <div class="device-info">
                    <p>Port: ${port}</p>
                    <p>Status: <span class="connection-status">${connectionText}</span></p>
                    <p>Last Reading: <span class="reading-value">${lastReading}</span></p>
                    <p>Last Update: ${lastUpdate}</p>
                    <p>MQTT Topic: nomuda/gvl/tools/GVB-Hioki-RM3545/${hioki_id}/result/impedance</p>
                    ${!isConnected ? '<div class="offline-message">Offline - Check connection or remove</div>' : ''}
                </div>
            </div>
        `;
    }

    // Remove device
    window.removeDevice = async (port) => {
        try {
            const response = await fetch(`/api/remove_device/${encodeURIComponent(port)}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            if (result.status === 'success') {
                addLogEntry(`Device removed from ${port}`, 'info');
                loadDevices();
            } else {
                addLogEntry(`Failed to remove device: ${result.message}`, 'error');
            }
        } catch (error) {
            addLogEntry(`Error removing device: ${error}`, 'error');
        }
    };
    

    // Test reading (toggle test mode)
    window.testReading = async (port) => {
        const deviceCard = document.getElementById(`device-${port.replace(/[^a-zA-Z0-9]/g, '')}`);
        const isTesting = deviceCard && deviceCard.classList.contains('device-card-testing');
        try {
            const response = await fetch(`/api/test_mode/${encodeURIComponent(port)}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ enable: !isTesting })
            });
            const result = await response.json();

            if (result.status === 'success') {
                loadDevices();
                addLogEntry(isTesting
                    ? `Stopped test reading from ${port}`
                    : `Started test reading from ${port}. Values will NOT be published to MQTT.`, 'info');
            } else {
                addLogEntry(`Failed to toggle test mode: ${result.message || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            addLogEntry(`Error toggling test mode: ${error}`, 'error');
        }
    };

    async function updateMqttStatus() {
        try {
            const res = await fetch('/api/mqtt_status');
            const data = await res.json();
            const indicator = document.getElementById('mqttIndicator');
            if (data.connected) {
                indicator.classList.remove('status-disconnected');
                indicator.classList.add('status-connected');
                indicator.title = data.message || "MQTT Connected";
            } else {
                indicator.classList.remove('status-connected');
                indicator.classList.add('status-disconnected');
                indicator.title = data.message || "MQTT Disconnected";
            }
        } catch (e) {
            const indicator = document.getElementById('mqttIndicator');
            indicator.classList.remove('status-connected');
            indicator.classList.add('status-disconnected');
            indicator.title = "MQTT Disconnected";
        }
    }

    async function loadDevices() {
        try {
            const response = await fetch('/api/devices');
            const data = await response.json();
            const devices = data.devices || {};
    
            deviceList.innerHTML = '';
            Object.entries(devices).forEach(([port, deviceData]) => {
                deviceList.innerHTML += createDeviceCard(port, deviceData);
            });
    
            // Update connected device count
            let connectedCount = Object.values(devices).filter(d => d.connected).length;
            document.getElementById('connectedDevices').textContent = `Connected Devices: ${connectedCount}`;
    
        } catch (error) {
            addLogEntry(`Error loading devices: ${error}`, 'error');
        }
    }

    // Clear log
    clearLogBtn.addEventListener('click', () => {
        logContainer.innerHTML = '';
    });

    // Log helper
    function addLogEntry(message, type = 'info') {
        const entry = document.createElement('div');
        entry.className = `log-entry log-${type}`;
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContainer.insertBefore(entry, logContainer.firstChild);
    }

    // Clear log entries
    function clearLogs() {
        logContainer.innerHTML = '';
    }

    // Initialize
    deviceForm.addEventListener('submit', addDevice);
    loadComPorts();
    loadDevices();
    updateMqttStatus();
    setInterval(loadComPorts, 10000); // Refresh port list every 10s
    setInterval(loadDevices, 5000); // Refresh devices every 5s
    setInterval(updateMqttStatus, 5000); // Update MQTT status every 5s
    clearLogButton.addEventListener('click', clearLogs);
});
