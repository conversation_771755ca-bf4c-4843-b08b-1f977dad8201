import sqlite3
import json
import logging
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union

logger = logging.getLogger(__name__)

# Determine the correct database path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_PATH = os.path.join(BASE_DIR, "hexmes_unified.db")

# Make sure the directory exists
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)


def init_db():
    """Initialize the unified database schema with improved error handling."""

    # Make sure the directory exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

    try:
        with sqlite3.connect(DB_PATH, timeout=30.0) as conn:
            cursor = conn.cursor()

            # Check if we can access the database
            try:
                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' LIMIT 1"
                )
            except sqlite3.OperationalError:
                logger.warning(
                    "Database appears to be locked or corrupted, attempting to fix..."
                )

            # Create tables with IF NOT EXISTS to avoid conflicts
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS devices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    entity_type TEXT NOT NULL DEFAULT 'unknown',
                    hioki_id TEXT,
                    ip TEXT,
                    port TEXT,
                    status TEXT DEFAULT 'unknown',
                    connection_method TEXT DEFAULT 'unknown',
                    config TEXT DEFAULT '{}',
                    last_reading TEXT,
                    last_command_id INTEGER,
                    last_update TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS device_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id INTEGER,
                    entity_type TEXT NOT NULL,
                    raw_data TEXT NOT NULL,
                    processed_data TEXT,
                    result_type TEXT,
                    mqtt_topic TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    command_id INTEGER
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS device_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id INTEGER,
                    entity_type TEXT NOT NULL,
                    tool_command TEXT,
                    command_data TEXT NOT NULL,
                    context TEXT,
                    mqtt_topic TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS api_tokens (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token TEXT NOT NULL UNIQUE,
                    username TEXT,
                    expires_at TIMESTAMP
                )
            """
            )

            # Create indexes (ignore errors if they already exist)
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_devices_entity_type ON devices (entity_type)",
                "CREATE INDEX IF NOT EXISTS idx_devices_hioki_id ON devices (hioki_id)",
                "CREATE INDEX IF NOT EXISTS idx_results_device_id ON device_results (device_id)",
                "CREATE INDEX IF NOT EXISTS idx_results_timestamp ON device_results (timestamp DESC)",
                "CREATE INDEX IF NOT EXISTS idx_commands_device_id ON device_commands (device_id)",
                "CREATE INDEX IF NOT EXISTS idx_commands_timestamp ON device_commands (timestamp DESC)",
                "CREATE INDEX IF NOT EXISTS idx_commands_processed ON device_commands (processed)",
            ]

            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                except Exception as e:
                    logger.warning(
                        f"Index creation warning (probably already exists): {e}"
                    )

            conn.commit()
            logger.info("Database initialization completed")

    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


def get_db_connection():
    """Get database connection with row factory and error handling."""
    try:
        conn = sqlite3.connect(DB_PATH, timeout=10.0)
        conn.row_factory = sqlite3.Row
        # Test the connection
        conn.execute("SELECT 1").fetchone()
        return conn
    except sqlite3.OperationalError as e:
        logger.error(f"Database connection error: {e}")
        # Try to reinitialize database
        try:
            init_db()
            conn = sqlite3.connect(DB_PATH, timeout=10.0)
            conn.row_factory = sqlite3.Row
            return conn
        except Exception as init_error:
            logger.error(f"Database reinitialization failed: {init_error}")
            raise
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        raise


# =============================================================================
# Device Management
# =============================================================================


def add_device(
    name: str,
    entity_type: str,
    connection_method: str,
    hioki_id: str = None,
    ip: str = None,
    port: str = None,
    config: Dict = None,
) -> int:
    """Add a new device to the unified table."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO devices (name, entity_type, hioki_id, ip, port, 
                               connection_method, config, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'offline')
        """,
            (
                name,
                entity_type,
                hioki_id,
                ip,
                port,
                connection_method,
                json.dumps(config) if config else None,
            ),
        )
        conn.commit()
        return cursor.lastrowid


def get_devices(entity_type: str = None) -> List[Dict]:
    """Get all devices, optionally filtered by entity type."""
    try:
        conn = get_db_connection()
        try:
            cursor = conn.cursor()
            if entity_type:
                cursor.execute(
                    "SELECT * FROM devices WHERE entity_type = ? ORDER BY name",
                    (entity_type,),
                )
            else:
                cursor.execute("SELECT * FROM devices ORDER BY entity_type, name")

            devices = []
            for row in cursor.fetchall():
                try:
                    device = dict(row)
                    if device.get("config"):
                        try:
                            device["config"] = json.loads(device["config"])
                        except (json.JSONDecodeError, TypeError):
                            device["config"] = {}
                    else:
                        device["config"] = {}
                    devices.append(device)
                except Exception as row_error:
                    logger.error(f"Error processing device row: {row_error}")
                    continue

            return devices
        finally:
            conn.close()

    except sqlite3.OperationalError as e:
        logger.error(f"Database operational error in get_devices: {e}")
        return []
    except Exception as e:
        logger.error(f"Error getting devices: {e}")
        return []


def get_device_by_id(device_id: int) -> Optional[Dict]:
    """Get a device by its ID."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM devices WHERE id = ?", (device_id,))
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device
        return None


def get_device_by_hioki_id(hioki_id: str, entity_type: str = None) -> Optional[Dict]:
    """Get a device by its Hioki ID."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        if entity_type:
            cursor.execute(
                "SELECT * FROM devices WHERE hioki_id = ? AND entity_type = ?",
                (hioki_id, entity_type),
            )
        else:
            cursor.execute("SELECT * FROM devices WHERE hioki_id = ?", (hioki_id,))
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device
        return None


def get_device_by_cts_id(cts_id: str) -> Optional[Dict]:
    """Get a CTS device by its CTS ID."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        # Try to find by hioki_id field (which is used for cts_id in CTS devices)
        cursor.execute(
            "SELECT * FROM devices WHERE hioki_id = ? AND entity_type = 'cts'",
            (cts_id,),
        )
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device

        # Fallback: try to find by name or in config
        cursor.execute(
            "SELECT * FROM devices WHERE entity_type = 'cts' AND (name LIKE ? OR config LIKE ?)",
            (f"%{cts_id}%", f"%{cts_id}%"),
        )
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device

        return None


def get_device_by_welder_id(welder_id: str) -> Optional[Dict]:
    """Get an ultrasonic welder device by its welder ID."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        # Try to find by name containing the welder_id
        cursor.execute(
            "SELECT * FROM devices WHERE entity_type = 'ultrasonic' AND name LIKE ?",
            (f"%{welder_id}%",),
        )
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device

        # Fallback: try to find in config
        cursor.execute(
            "SELECT * FROM devices WHERE entity_type = 'ultrasonic' AND config LIKE ?",
            (f"%{welder_id}%",),
        )
        row = cursor.fetchone()
        if row:
            device = dict(row)
            if device["config"]:
                try:
                    device["config"] = json.loads(device["config"])
                except json.JSONDecodeError:
                    device["config"] = {}
            else:
                device["config"] = {}
            return device

        return None


def update_device_status(device_id: int, status: str):
    """Update device status."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE devices 
            SET status = ?, last_update = CURRENT_TIMESTAMP 
            WHERE id = ?
        """,
            (status, device_id),
        )
        conn.commit()


def update_device_last_reading(device_id: int, reading: str):
    """Update device's last reading."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE devices 
            SET last_reading = ?, last_update = CURRENT_TIMESTAMP 
            WHERE id = ?
        """,
            (reading, device_id),
        )
        conn.commit()


def remove_device(device_id: int) -> bool:
    """Remove a device and all its associated data."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        # Remove associated results and commands
        cursor.execute("DELETE FROM device_results WHERE device_id = ?", (device_id,))
        cursor.execute("DELETE FROM device_commands WHERE device_id = ?", (device_id,))
        # Remove device
        cursor.execute("DELETE FROM devices WHERE id = ?", (device_id,))
        conn.commit()
        return cursor.rowcount > 0


def delete_device(device_id: int) -> bool:
    """Delete a device (alias for remove_device for API compatibility)."""
    return remove_device(device_id)


# =============================================================================
# Command Handling
# =============================================================================


def store_device_command(entity_type: str, tool_name: str, command_data: Dict) -> int:
    """Store an incoming device command."""
    tool_command = command_data.get("toolCommand", "")
    context = command_data.get("context", {})

    # Try to find the device this command is for
    device_id = None
    device = find_device_for_command(entity_type, tool_name, context)
    if device:
        device_id = device["id"]

    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO device_commands (device_id, entity_type, tool_command, 
                                       command_data, context, mqtt_topic, processed)
            VALUES (?, ?, ?, ?, ?, ?, FALSE)
        """,
            (
                device_id,
                entity_type,
                tool_command,
                json.dumps(command_data),
                json.dumps(context),
                f"nomuda/gvl/tools/{entity_type}/{tool_name}/command",
            ),
        )
        conn.commit()
        return cursor.lastrowid


def find_device_for_command(
    entity_type: str, tool_name: str, context: Dict
) -> Optional[Dict]:
    """Find which device a command is intended for based on MQTT topic structure.

    Expected topic patterns:
    - nomuda/gvl/tools/GVB-Hioki-RM3545/H005/command
    - nomuda/gvl/tools/GVB-CTS-Manifold/CTS001/command
    - nomuda/gvl/tools/GVB-BRANSON/WELDER001/command

    Args:
        entity_type: The device entity type (hioki, cts, ultrasonic)
        tool_name: The device identifier from the MQTT topic (H005, CTS001, etc.)
        context: Command context (not used for topic-based lookup)

    Returns:
        Device dictionary if found, None otherwise
    """

    # For Hioki devices: tool_name should be the hioki_id (e.g., "H005")
    if entity_type.lower() in ["hioki", "gvb-hioki-rm3545"]:
        device = get_device_by_hioki_id(tool_name, "hioki")
        if device:
            logger.info(
                f"Found Hioki device for command: {tool_name} -> {device['name']}"
            )
            return device
        else:
            logger.warning(f"No Hioki device found for hioki_id: {tool_name}")

    # For CTS devices: tool_name should be the cts_id (e.g., "CTS001")
    elif entity_type.lower() in ["cts", "gvb-cts-manifold", "gvb-cts-enclosure"]:
        device = get_device_by_cts_id(tool_name)
        if device:
            logger.info(
                f"Found CTS device for command: {tool_name} -> {device['name']}"
            )
            return device
        else:
            logger.warning(f"No CTS device found for cts_id: {tool_name}")

    # For Ultrasonic/Welder devices: tool_name should be the welder identifier
    elif entity_type.lower() in ["ultrasonic", "welder", "gvb-branson"]:
        device = get_device_by_welder_id(tool_name)
        if device:
            logger.info(
                f"Found ultrasonic device for command: {tool_name} -> {device['name']}"
            )
            return device
        else:
            logger.warning(f"No ultrasonic device found for welder_id: {tool_name}")

    # Fallback: try to find by name matching
    else:
        devices = get_devices(entity_type.lower())
        for device in devices:
            # Check if tool_name matches device name or any identifier
            if (
                tool_name.lower() in device["name"].lower()
                or tool_name.lower() == device.get("hioki_id", "").lower()
                or tool_name.lower() == device.get("cts_id", "").lower()
            ):
                logger.info(
                    f"Found device by name matching: {tool_name} -> {device['name']}"
                )
                return device

    logger.error(
        f"No device found for entity_type: {entity_type}, tool_name: {tool_name}"
    )
    return None


def get_recent_commands(
    device_id: int = None,
    entity_type: str = None,
    unprocessed_only: bool = False,
    limit: int = 100,
) -> List[Dict]:
    """Get recent commands, optionally filtered."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        query = "SELECT * FROM device_commands WHERE 1=1"
        params = []

        if device_id:
            query += " AND device_id = ?"
            params.append(device_id)
        if entity_type:
            query += " AND entity_type = ?"
            params.append(entity_type)
        if unprocessed_only:
            query += " AND processed = 0"

        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)

        cursor.execute(query, params)
        commands = []
        for row in cursor.fetchall():
            cmd = dict(row)
            try:
                cmd["command_data"] = (
                    json.loads(cmd["command_data"]) if cmd["command_data"] else {}
                )
                cmd["context"] = json.loads(cmd["context"]) if cmd["context"] else {}
            except json.JSONDecodeError:
                pass
            commands.append(cmd)
        return commands


def mark_command_processed(command_id: int):
    """Mark a command as processed."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE device_commands SET processed = 1 WHERE id = ?", (command_id,)
        )
        conn.commit()


# =============================================================================
# Result Processing
# =============================================================================


def store_device_result(
    device_id: int,
    entity_type: str,
    raw_data: str,
    result_type: str,
    mqtt_topic: str = None,
    command_id: int = None,
) -> int:
    """Store a device result and process it according to its type."""

    # Process the data based on entity type
    processed_data = None
    if entity_type == "cts":
        processed_data = process_cts_result(raw_data)
    elif entity_type == "hioki":
        processed_data = process_hioki_result(raw_data)
    elif entity_type == "ultrasonic":
        processed_data = process_ultrasonic_result(raw_data)

    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO device_results (device_id, entity_type, raw_data, 
                                      processed_data, result_type, mqtt_topic, command_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """,
            (
                device_id,
                entity_type,
                raw_data,
                json.dumps(processed_data) if processed_data else None,
                result_type,
                mqtt_topic,
                command_id,
            ),
        )
        conn.commit()
        result_id = cursor.lastrowid

        # Update device's last reading
        if device_id:
            update_device_last_reading(device_id, raw_data)

        return result_id


def get_device_results(device_id: int, limit: int = 100) -> List[Dict]:
    """Get recent results for a device."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT * FROM device_results 
            WHERE device_id = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
        """,
            (device_id, limit),
        )

        results = []
        for row in cursor.fetchall():
            result = dict(row)
            if result["processed_data"]:
                try:
                    result["processed_data"] = json.loads(result["processed_data"])
                except json.JSONDecodeError:
                    pass
            results.append(result)
        return results


def process_cts_result(raw_data: str) -> Optional[Dict]:
    """Process CTS result data similar to the NodeRED logic."""
    try:
        # Filter out messages with "*" or "x01"
        if "*" in raw_data or "x01" in raw_data:
            return None

        # Split the payload
        parts = raw_data.split()
        if not parts:
            return None

        def search_pattern(pattern: str, arr: List[str]) -> int:
            """Find index of exact pattern match."""
            for i, val in enumerate(arr):
                if val == pattern:
                    return i
            return -1

        def contains_substring(substring: str, arr: List[str]) -> int:
            """Find index of first item containing substring."""
            for i, val in enumerate(arr):
                if substring in val:
                    return i
            return -1

        # Find indices similar to NodeRED logic
        msg1_idx = search_pattern("dpsig", parts)
        msg2_idx = search_pattern("psig", parts)
        msg3_idx = contains_substring("|GVB|", parts)

        # Extract values
        pressureloss = parts[msg1_idx - 1] if msg1_idx > 0 else None
        testpressure = parts[msg2_idx - 1] if msg2_idx > 0 else None
        serial = parts[msg3_idx] if msg3_idx != -1 else None

        # Create processed array similar to NodeRED final node
        processed_array = []
        indices = [8, 6, 2, 4, 5, 10, 12, 14, 15, 17, 18]
        for idx in indices:
            if idx < len(parts):
                processed_array.append(parts[idx])
            else:
                processed_array.append("")

        return {
            "pressureloss": pressureloss,
            "testpressure": testpressure,
            "serial": serial,
            "processed_array": processed_array,
            "raw_parts": parts,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error processing CTS result: {e}")
        return None


def process_hioki_result(raw_data: str) -> Optional[Dict]:
    """Process Hioki impedance result."""
    try:
        # Convert from scientific notation to milliohms
        value = float(raw_data.strip()) * 1000
        formatted_value = f"{value:.4f}"

        return {
            "impedance": formatted_value,
            "unit": "mΩ",
            "raw_value": raw_data.strip(),
            "timestamp": datetime.now().isoformat(),
        }
    except (ValueError, TypeError) as e:
        logger.error(f"Error processing Hioki result: {e}")
        return None


def process_ultrasonic_result(raw_data: str) -> Optional[Dict]:
    """Process ultrasonic welder result (already JSON)."""
    try:
        if isinstance(raw_data, str):
            data = json.loads(raw_data)
        else:
            data = raw_data

        # Extract key metrics from the "last" object
        last_data = data.get("last", {})

        return {
            "weld_result": data.get("ok", False),
            "energy": last_data.get("energy"),
            "power": last_data.get("weld_power"),
            "time": last_data.get("weld_time"),
            "amplitude": last_data.get("amplitude"),
            "pressure": last_data.get("pressure"),
            "height": last_data.get("height"),
            "part_name": last_data.get("part_name"),
            "cycle_count": last_data.get("cycle_count"),
            "timestamp": last_data.get("timestamp", datetime.now().isoformat()),
            "full_data": data,
        }
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"Error processing ultrasonic result: {e}")
        return None


def link_command_to_result(command_id: int, result_id: int):
    """Link a command to a result."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE device_results 
            SET command_id = ? 
            WHERE id = ?
        """,
            (command_id, result_id),
        )
        cursor.execute(
            """
            UPDATE devices 
            SET last_command_id = ? 
            WHERE id = (SELECT device_id FROM device_results WHERE id = ?)
        """,
            (command_id, result_id),
        )
        conn.commit()


def find_matching_command(
    device_id: int,
    entity_type: str,
    result_timestamp: datetime,
    time_window_minutes: int = 10,
) -> Optional[Dict]:
    """Find a command that might match this result."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT * FROM device_commands 
            WHERE (device_id = ? OR device_id IS NULL) 
            AND entity_type = ?
            AND processed = 0
            AND datetime(timestamp) >= datetime(?, '-{} minutes')
            AND datetime(timestamp) <= datetime(?, '+{} minutes')
            ORDER BY timestamp DESC
            LIMIT 1
        """.format(
                time_window_minutes, time_window_minutes
            ),
            (
                device_id,
                entity_type,
                result_timestamp.isoformat(),
                result_timestamp.isoformat(),
            ),
        )

        row = cursor.fetchone()
        if row:
            cmd = dict(row)
            try:
                cmd["command_data"] = (
                    json.loads(cmd["command_data"]) if cmd["command_data"] else {}
                )
                cmd["context"] = json.loads(cmd["context"]) if cmd["context"] else {}
            except json.JSONDecodeError:
                pass
            return cmd
        return None


# =============================================================================
# API Token Management
# =============================================================================


def create_api_token(username: str, expire_minutes: int = None) -> str:
    """Create an API token for authentication."""
    import secrets
    from datetime import timezone, timedelta

    token = secrets.token_hex(32)
    expires_at = None
    if expire_minutes is not None:
        expires_at = (
            datetime.now(timezone.utc) + timedelta(minutes=expire_minutes)
        ).isoformat()

    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO api_tokens (token, username, expires_at) VALUES (?, ?, ?)",
            (token, username, expires_at),
        )
        conn.commit()
    return token


def validate_api_token(token: str) -> Optional[str]:
    """Validate an API token and return username if valid."""
    if not token:
        return None

    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT username, expires_at FROM api_tokens WHERE token = ?",
            (token,),
        )
        row = cursor.fetchone()
        if not row:
            return None

        username, expires_at = row
        if expires_at is None:
            return username

        try:
            expires_dt = datetime.fromisoformat(expires_at)
            if expires_dt >= datetime.now(timezone.utc):
                return username
        except Exception:
            pass

    return None


# =============================================================================
# Utility Functions
# =============================================================================


def get_last_reading(entity_type: str, identifier: str) -> Optional[Dict]:
    """Get the most recent reading for a device."""
    with get_db_connection() as conn:
        cursor = conn.cursor()

        if entity_type == "hioki":
            cursor.execute(
                "SELECT last_reading, last_update FROM devices WHERE hioki_id = ? AND entity_type = 'hioki'",
                (identifier,),
            )
            row = cursor.fetchone()
            if row:
                return {
                    "entity_type": "hioki",
                    "device_id": identifier,
                    "impedance": row["last_reading"],
                    "timestamp": row["last_update"],
                }

        elif entity_type == "cts":
            cursor.execute(
                """
                SELECT last_reading, last_update
                FROM devices
                WHERE (id = ? OR hioki_id = ?) AND entity_type = 'cts'
                """,
                (identifier, identifier),
            )
            row = cursor.fetchone()
            if row:
                try:
                    data = (
                        json.loads(row["last_reading"]) if row["last_reading"] else {}
                    )
                except json.JSONDecodeError:
                    data = {}
                return {
                    "entity_type": "cts",
                    "device_id": identifier,
                    "testpressure": data.get("testpressure"),
                    "pressureloss": data.get("pressureloss"),
                    "timestamp": row["last_update"],
                }

        elif entity_type in ("welder", "ultrasonic"):
            cursor.execute(
                """
                SELECT last_reading, last_update
                FROM devices
                WHERE (id = ? OR name = ?) AND entity_type = 'ultrasonic'
                """,
                (identifier, identifier),
            )
            row = cursor.fetchone()
            if row:
                try:
                    data = (
                        json.loads(row["last_reading"]) if row["last_reading"] else {}
                    )
                except json.JSONDecodeError:
                    data = {"raw": row["last_reading"]}
                result = {
                    "entity_type": "ultrasonic",
                    "device_id": identifier,
                    "timestamp": row["last_update"],
                }
                if isinstance(data, dict):
                    result.update(data)
                else:
                    result["last_reading"] = data
                return result

        return None


# =============================================================================
# Service Class for Compatibility
# =============================================================================


class UnifiedDBService:
    """Unified database service class for compatibility."""

    def __init__(self):
        pass

    def get_db_connection(self):
        """Get database connection."""
        return get_db_connection()

    def get_devices(self, entity_type: str = None) -> List[Dict]:
        """Get devices by type."""
        return get_devices(entity_type)

    def get_devices_by_type(self, entity_type: str) -> List[Dict]:
        """Get devices by specific type."""
        return get_devices(entity_type)

    def get_device_by_id(self, device_id: int) -> Optional[Dict]:
        """Get device by ID."""
        return get_device_by_id(device_id)

    def get_device_by_hioki_id(
        self, hioki_id: str, entity_type: str = None
    ) -> Optional[Dict]:
        """Get device by Hioki ID."""
        return get_device_by_hioki_id(hioki_id, entity_type)

    def store_device_result(
        self,
        device_identifier: str,
        entity_type: str,
        raw_data: str,
        parsed_data: Dict = None,
    ) -> int:
        """Store device result."""
        # Find device by identifier
        device = None
        if entity_type == "hioki_w610":
            device = get_device_by_hioki_id(device_identifier, "hioki")
        else:
            devices = get_devices(entity_type)
            for d in devices:
                if (
                    d.get("hioki_id") == device_identifier
                    or d.get("name") == device_identifier
                ):
                    device = d
                    break

        if device:
            return store_device_result(
                device_id=device["id"],
                entity_type=entity_type,
                raw_data=raw_data,
                result_type="reading",
                mqtt_topic=None,
                command_id=None,
            )
        return None

    def get_device_results(
        self, device_identifier: str, entity_type: str, limit: int = 100
    ) -> List[Dict]:
        """Get device results."""
        # Find device by identifier
        device = None
        if entity_type == "hioki_w610":
            device = get_device_by_hioki_id(device_identifier, "hioki")
        else:
            devices = get_devices(entity_type)
            for d in devices:
                if (
                    d.get("hioki_id") == device_identifier
                    or d.get("name") == device_identifier
                ):
                    device = d
                    break

        if device:
            return get_device_results(device["id"], limit)
        return []

    def get_last_reading(self, entity_type: str, identifier: str) -> Optional[Dict]:
        """Get last reading."""
        return get_last_reading(entity_type, identifier)

    def delete_device(self, device_id: int) -> bool:
        """Delete a device."""
        return delete_device(device_id)

    def remove_device(self, device_id: int) -> bool:
        """Remove a device (alias for delete_device)."""
        return delete_device(device_id)

    def execute_query(
        self,
        query: str,
        params: tuple = None,
        fetch_one: bool = False,
        fetch_all: bool = False,
    ):
        """Execute a raw SQL query with optional parameters."""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if fetch_one:
                    result = cursor.fetchone()
                    return dict(result) if result else None
                elif fetch_all:
                    results = cursor.fetchall()
                    return [dict(row) for row in results] if results else []
                else:
                    # For INSERT/UPDATE/DELETE operations, return lastrowid or rowcount
                    conn.commit()
                    return cursor.lastrowid if cursor.lastrowid else cursor.rowcount

        except Exception as e:
            logger.error(f"Error executing query: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise


# Create global service instance
unified_service = UnifiedDBService()
