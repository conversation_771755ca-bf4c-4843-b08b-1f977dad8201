import sqlite3
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)


class DBService:
    def __init__(self, db_path):
        self.db_path = db_path
        self._init_db()

    def _init_db(self):
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        with self._get_connection() as conn:
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS devices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    port TEXT NOT NULL UNIQUE,
                    hioki_id TEXT NOT NULL,
                    last_reading TEXT,
                    last_update TIMESTAMP,
                    connected INTEGER DEFAULT 1,
                    disconnected_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS bridge_info (
                    id INTEGER PRIMARY KEY CHECK (id = 1),
                    bridge_id TEXT NOT NULL
                )
                """
            )
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS api_tokens (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token TEXT NOT NULL UNIQUE,
                    username TEXT,
                    expires_at TIMESTAMP
                )
                """
            )
            conn.commit()

    def _get_connection(self):
        return sqlite3.connect(self.db_path)

    def save_device(self, port, hioki_id):
        with self._get_connection() as conn:
            conn.execute(
                "INSERT OR REPLACE INTO devices (port, hioki_id, connected) VALUES (?, ?, 1)",
                (port, hioki_id),
            )
            conn.commit()

    def update_reading(self, port, reading, timestamp):
        with self._get_connection() as conn:
            conn.execute(
                "UPDATE devices SET last_reading = ?, last_update = ? WHERE port = ?",
                (reading, timestamp, port),
            )
            conn.commit()

    def mark_offline(self, port, timestamp):
        with self._get_connection() as conn:
            conn.execute(
                "UPDATE devices SET connected = 0, disconnected_at = ? WHERE port = ?",
                (timestamp, port),
            )
            conn.commit()

    def mark_online(self, port):
        with self._get_connection() as conn:
            conn.execute(
                "UPDATE devices SET connected = 1, disconnected_at = NULL WHERE port = ?",
                (port,),
            )
            conn.commit()

    def load_devices(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM devices")
            return [
                dict(zip([col[0] for col in cursor.description], row))
                for row in cursor.fetchall()
            ]

    def remove_device(self, port, hioki_id=None):
        """Remove a device by port, falling back to hioki_id if needed."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM devices WHERE port = ?", (port,))
            if cursor.rowcount == 0 and hioki_id is not None:
                cursor.execute("DELETE FROM devices WHERE hioki_id = ?", (hioki_id,))
            conn.commit()

    def get_bridge_id(self):
        """Retrieve the persistent bridge ID if available"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT bridge_id FROM bridge_info WHERE id = 1")
            row = cursor.fetchone()
            return row[0] if row else None

    def set_bridge_id(self, bridge_id):
        """Store or update the persistent bridge ID"""
        with self._get_connection() as conn:
            conn.execute(
                "INSERT OR REPLACE INTO bridge_info (id, bridge_id) VALUES (1, ?)",
                (bridge_id,),
            )
            conn.commit()

    # ------------------------------------------------------------------
    # API token utilities

    def create_api_token(self, username, expire_minutes=60):
        """Create and store an API token."""
        import secrets
        from datetime import timedelta

        token = secrets.token_hex(32)
        expires_at = (datetime.utcnow() + timedelta(minutes=expire_minutes)).isoformat()
        with self._get_connection() as conn:
            conn.execute(
                "INSERT INTO api_tokens (token, username, expires_at) VALUES (?, ?, ?)",
                (token, username, expires_at),
            )
            conn.commit()
        return token

    def validate_api_token(self, token):
        """Validate a token and return the associated username if valid."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT username, expires_at FROM api_tokens WHERE token = ?",
                (token,),
            )
            row = cursor.fetchone()
            if not row:
                return None
            username, expires_at = row
            try:
                exp = datetime.fromisoformat(expires_at)
                if exp >= datetime.utcnow():
                    return username
            except Exception:
                pass
        return None

    def delete_expired_tokens(self):
        """Remove expired tokens from the database."""
        with self._get_connection() as conn:
            conn.execute(
                "DELETE FROM api_tokens WHERE expires_at < ?",
                (datetime.utcnow().isoformat(),),
            )
            conn.commit()

