/* Main styles */
body {
    font-family: 'Gotham', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0D0C33;
    color: #e0e0e0;
}

/* Navbar and logo */
.proterra-logo {
    height: 30px;
    width: auto;
}

.hexmes-logo {
    height: 30px;
    width: auto;
}

.hexmes-logo-lg {
    height: 40px;
    width: auto;
}

.navbar,
.footer {
    background-color: #1A1850;
}

.navbar-brand {
    font-weight: 500;
}

/* Card styles */
.card {
    background-color: #1A1850;
    border-color: #333;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: #0D0C33;
    border-bottom-color: #333;
}

.card-footer {
    background-color: #0D0C33;
    border-top-color: #333;
}

/* Status badges */
.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
}

/* Bridge cards */
.bridge-card {
    transition: all 0.2s ease;
}

.bridge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* Status colors */
.status-online {
    color: #82EB6A;
}

.status-offline {
    color: #AE3D58;
}

.status-unknown {
    color: #BDD0D7;
}

/* Form controls */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

.form-control:focus, .form-select:focus {
    background-color: #333;
    border-color: #18DFD5;
    color: #fff;
}

/* Table styles */
.table {
    color: #e0e0e0;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Device list */
.connected-devices {
    max-height: 250px;
    overflow-y: auto;
}

/* Footer */
.footer {
    margin-top: 3rem;
    border-top: 1px solid #333;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: #18DFD5;
    border-color: #18DFD5;
    color: #0D0C33;
}

.btn-primary:hover {
    background-color: #14b5ae;
    border-color: #14b5ae;
    color: #0D0C33;
}

/* Modal styling */
.modal-content {
    background-color: #2d2d2d;
    border-color: #444;
}

.modal-header, .modal-footer {
    border-color: #444;
}

/* Alert styling */
.alert-info {
    background-color: #18DFD5;
    border-color: #023F38;
    color: #0D0C33;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .proterra-logo {
        height: 24px;
    }
}

/* Highlight devices in testing mode */
.table-warning {
    background-color: #fff3cd;
}

/* Add a testing indicator */
tr.table-warning td:first-child::before {
    content: "🧪 ";
}

/* Device card styles */
.device-card {
    /* ...existing styles... */
}

.device-card.testing {
    border: 2px solid #ffd700;
}

.device-status {
    padding: 4px 8px;
    border-radius: 4px;
}

.status-active {
    background-color: #82EB6A;
    color: #0D0C33;
}

.status-inactive {
    background-color: #AE3D58;
    color: white;
}

.status-unknown {
    background-color: #BDD0D7;
    color: #0D0C33;
}

/* Port status styles */
.port-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.port-status.text-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.port-status.text-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Ultrasonic welder data container */
.welder-results {
    max-height: 150px;
    overflow-y: auto;
    background-color: #2d2d2d;
    border: 1px solid #444;
}

/* Enhanced Welder Card Styles */
.welder-data-container {
    max-height: 200px;
    overflow-y: auto;
    background-color: rgba(248, 249, 250, 0.8);
    border-radius: 0.375rem;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.welder-data-container:hover {
    background-color: rgba(248, 249, 250, 1);
    border-color: #18DFD5;
}

[data-bs-theme="dark"] .welder-data-container {
    background-color: rgba(45, 45, 45, 0.8);
    border-color: #444;
}

[data-bs-theme="dark"] .welder-data-container:hover {
    background-color: rgba(45, 45, 45, 1);
    border-color: #18DFD5;
}

.welder-data-grid {
    font-size: 0.85rem;
    line-height: 1.4;
}

.welder-data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(233, 236, 239, 0.5);
    transition: background-color 0.15s ease;
}

.welder-data-item:hover {
    background-color: rgba(24, 223, 213, 0.05);
    border-radius: 0.25rem;
}

[data-bs-theme="dark"] .welder-data-item {
    border-bottom-color: rgba(68, 68, 68, 0.5);
}

[data-bs-theme="dark"] .welder-data-item:hover {
    background-color: rgba(24, 223, 213, 0.1);
}

.welder-data-item:last-child {
    border-bottom: none;
}

.welder-data-key {
    font-weight: 500;
    color: #6c757d;
    flex: 0 0 45%;
    font-size: 0.8rem;
    text-transform: capitalize;
}

[data-bs-theme="dark"] .welder-data-key {
    color: #adb5bd;
}

.welder-data-value {
    flex: 1;
    text-align: right;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.8rem;
    font-weight: 500;
}

.welder-nested-object {
    margin-top: 0.25rem;
    padding-left: 1rem;
    border-left: 2px solid #18DFD5;
    background-color: rgba(24, 223, 213, 0.02);
    border-radius: 0 0.25rem 0.25rem 0;
}

.welder-array-item {
    margin: 0.1rem 0;
    padding: 0.2rem 0.5rem;
    background-color: rgba(24, 223, 213, 0.1);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    border-left: 3px solid #18DFD5;
}

[data-bs-theme="dark"] .welder-array-item {
    background-color: rgba(24, 223, 213, 0.15);
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    animation: pulse 2s infinite;
}

.status-success { 
    background-color: #28a745; 
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-warning { 
    background-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.status-danger { 
    background-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

.status-info { 
    background-color: #17a2b8;
    box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.3);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Enhanced Card Footer */
.card-footer .btn-group-sm > .btn, .card-footer .btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.card-footer .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Improved scrollbar for welder data */
.welder-data-container::-webkit-scrollbar {
    width: 6px;
}

.welder-data-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.welder-data-container::-webkit-scrollbar-thumb {
    background: #18DFD5;
    border-radius: 3px;
    opacity: 0.7;
}

.welder-data-container::-webkit-scrollbar-thumb:hover {
    background: #14b5ae;
    opacity: 1;
}

/* Firefox scrollbar */
.welder-data-container {
    scrollbar-width: thin;
    scrollbar-color: #18DFD5 rgba(0, 0, 0, 0.05);
}

/* Raw Data Modal Enhancements */
#rawDataContent {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    resize: vertical;
    min-height: 300px;
}

.modal-header {
    background-color: rgba(24, 223, 213, 0.1);
    border-bottom-color: #18DFD5;
}

[data-bs-theme="dark"] .modal-header {
    background-color: rgba(24, 223, 213, 0.2);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .welder-data-key {
        flex: 0 0 50%;
        font-size: 0.75rem;
    }
    
    .welder-data-value {
        font-size: 0.75rem;
    }
    
    .card-footer {
        padding: 0.5rem;
    }
    
    .card-footer .btn-sm {
        padding: 0.2rem 0.3rem;
        font-size: 0.7rem;
        margin-right: 0.2rem;
        margin-bottom: 0.2rem;
    }
}

/* Loading state for welder cards */
.welder-loading {
    position: relative;
    overflow: hidden;
}

.welder-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(24, 223, 213, 0.2), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Text utilities for status colors */
.text-success {
    color: #28a745 !important;
    font-weight: 600;
}

.text-danger {
    color: #dc3545 !important;
    font-weight: 600;
}

.text-warning {
    color: #ffc107 !important;
    font-weight: 600;
}

.text-info {
    color: #17a2b8 !important;
    font-weight: 600;
}

[data-bs-theme="dark"] .text-success {
    color: #82EB6A !important;
}

[data-bs-theme="dark"] .text-danger {
    color: #ff6b7a !important;
}

[data-bs-theme="dark"] .text-warning {
    color: #ffd93d !important;
}

[data-bs-theme="dark"] .text-info {
    color: #3dd5f3 !important;
}

/* Icon spin animation for status checks */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
.spin {
    animation: spin 1s linear infinite;
}

/* Environmental monitoring loading indicators */
.environmental-loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.environmental-cache-time {
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.environmental-cache-time.updating {
    color: var(--bs-primary) !important;
}

/* Smooth transitions for loading states */
#weatherLoadingIndicator,
#dicksonLoadingIndicator {
    transition: opacity 0.3s ease;
}

/* Pulse animation for cache time when updating */
@keyframes pulse-cache {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.cache-updating {
    animation: pulse-cache 1.5s ease-in-out infinite;
}

/* Environmental card enhancements */
.environmental-card {
    transition: all 0.3s ease;
}

.environmental-card.loading {
    opacity: 0.8;
}

.environmental-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.environmental-status-indicator .spinner-border-sm {
    width: 0.8rem;
    height: 0.8rem;
}
