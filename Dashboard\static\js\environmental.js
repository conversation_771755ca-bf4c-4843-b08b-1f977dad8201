/**
 * Environmental monitoring JavaScript
 * Handles weather data, Dickson devices, and environmental alerts
 */

class EnvironmentalMonitor {
    constructor() {
        this.weatherUpdateInterval = null;
        this.dicksonUpdateInterval = null;
        this.alertsUpdateInterval = null;
        this.lastWeatherUpdate = null;
        this.lastDicksonUpdate = null;
        this.timeUpdateInterval = null;
        this.isWeatherLoading = false;
        this.isDicksonLoading = false;
        this.init();
    }

    init() {
        this.loadWeatherData();
        this.loadDicksonDevices();
        this.loadEnvironmentalAlerts();

        // Set up periodic updates
        this.weatherUpdateInterval = setInterval(() => this.loadWeatherData(), 10000); // 10 seconds
        this.dicksonUpdateInterval = setInterval(() => this.loadDicksonDevices(), 60000); // 1 minute
        this.alertsUpdateInterval = setInterval(() => this.loadEnvironmentalAlerts(), 30000); // 30 seconds

        // Set up real-time timestamp updates every second
        this.timeUpdateInterval = setInterval(() => this.updateTimestamps(), 1000); // 1 second
    }

async loadWeatherData() {
    try {
        // Set loading state
        this.isWeatherLoading = true;
        this.updateWeatherLoadingState();

        // Use your local HexMES weather endpoint instead of external API
        const response = await fetch('/api/environmental/weather');
        const result = await response.json();

        if (result.status === 'success' && result.data) {
            this.lastWeatherUpdate = new Date();
            this.displayWeatherData(result.data);
        } else {
            // Handle both API errors and weather service errors
            const message = result.message || 'Failed to load weather data';
            this.displayWeatherError(message);
        }
    } catch (error) {
        console.error('Error loading weather data:', error);
        this.displayWeatherError('Connection to Weather Service Unavailable');
    } finally {
        // Clear loading state
        this.isWeatherLoading = false;
        this.updateWeatherLoadingState();
    }
}

displayWeatherData(data) {
    const weatherCard = document.getElementById('weatherCard');
    
    // Handle different data formats (direct from proxy or from database)
    const temp_c = data.temperature_c || (data.processed_data && data.processed_data.temperature_c);
    const temp_f = data.temperature_f || (data.processed_data && data.processed_data.temperature_f);
    const humidity = data.humidity || (data.processed_data && data.processed_data.humidity);
    const description = data.description || (data.processed_data && data.processed_data.description) || 'Weather data';
    
    const timestamp = new Date(data.timestamp || data.last_update || new Date()).toLocaleString();
    
    // Store cache info for real-time updates
    this.weatherCacheData = data.cached ? {
        cached: true,
        cacheTime: new Date(Date.now() - (data.cache_age_seconds * 1000))
    } : null;

    weatherCard.innerHTML = `
        <div class="row text-center">
            <div class="col-6">
                <h4 class="text-info mb-1">${temp_c}°C</h4>
                <small class="text-muted">${temp_f}°F</small>
            </div>
            <div class="col-6">
                <h4 class="text-info mb-1">${humidity}%</h4>
                <small class="text-muted">Humidity</small>
            </div>
        </div>
        <div class="text-center mt-2">
            <p class="mb-1">${description}</p>
            <div class="d-flex align-items-center justify-content-center">
                <small class="text-muted">Updated: ${timestamp}</small>
                <span id="weatherLoadingIndicator" class="ms-2" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </span>
            </div>
            <div id="weatherCacheTime" class="mt-1"></div>
            ${data.mock_data ? '<small class="text-warning">(Mock Data)</small>' : ''}
        </div>
    `;

    // Update the cache time display
    this.updateWeatherCacheTime();
}

displayWeatherError(message) {
    const weatherCard = document.getElementById('weatherCard');
    weatherCard.innerHTML = `
        <div class="text-center text-danger">
            <i class="bi bi-exclamation-triangle fs-1"></i>
            <p class="mt-2 mb-1">Weather Unavailable</p>
            <small class="text-muted">${message}</small>
        </div>
    `;
}

displayWeatherError(message) {
    const weatherCard = document.getElementById('weatherCard');
    weatherCard.innerHTML = `
        <div class="text-center text-danger">
            <i class="bi bi-exclamation-triangle fs-1"></i>
            <p class="mt-2 mb-1">Weather Unavailable</p>
            <small class="text-muted">${message}</small>
        </div>
    `;
}

    async loadDicksonDevices() {
        try {
            // Set loading state
            this.isDicksonLoading = true;
            this.updateDicksonLoadingState();

            const response = await fetch('/api/environmental/dickson');
            const result = await response.json();

            if (result.status === 'success') {
                this.lastDicksonUpdate = new Date();
                this.displayDicksonDevices(result.devices);
            } else {
                this.displayDicksonError(result.message);
            }
        } catch (error) {
            console.error('Error loading Dickson devices:', error);
            this.displayDicksonError('Failed to load Dickson devices');
        } finally {
            // Clear loading state
            this.isDicksonLoading = false;
            this.updateDicksonLoadingState();
        }
    }

    displayDicksonDevices(devices) {
        const dicksonSummary = document.getElementById('dicksonSummary');
        
        if (devices.length === 0) {
            dicksonSummary.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-plus-circle fs-1"></i>
                    <p class="mt-2">No Dickson devices configured</p>
                    <a href="/devices/add" class="btn btn-warning btn-sm">Add Dickson Device</a>
                </div>
            `;
            return;
        }

        let html = '<div class="row">';
        
        devices.forEach(device => {
            const statusClass = device.status === 'online' ? 'success' : 'danger';
            const tempStatus = this.getTemperatureStatus(device);
            const humidityStatus = this.getHumidityStatus(device);
            
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card border-${statusClass}">
                        <div class="card-header bg-${statusClass} text-white d-flex justify-content-between">
                            <span><strong>${device.name}</strong></span>
                            <span class="badge bg-light text-dark">${device.status}</span>
                        </div>
                        <div class="card-body">
                            <p class="mb-1"><strong>Location:</strong> ${device.location}</p>
                            <p class="mb-1"><strong>Type:</strong> ${this.getLocationTypeName(device.location_type)}</p>
                            
                            ${device.last_temperature !== null ? `
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <span class="badge bg-${tempStatus.class}">${device.last_temperature}°C</span>
                                        <small class="d-block text-muted">Temperature</small>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge bg-${humidityStatus.class}">${device.last_humidity}%</span>
                                        <small class="d-block text-muted">Humidity</small>
                                    </div>
                                </div>
                            ` : '<p class="text-muted">No recent readings</p>'}
                            
                            ${device.last_reading_time ? `
                                <small class="text-muted">Last reading: ${new Date(device.last_reading_time).toLocaleString()}</small>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';

        // Add loading indicator and cache time
        html += `
            <div class="d-flex align-items-center justify-content-center mt-2">
                <span id="dicksonLoadingIndicator" class="me-2" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </span>
                <div id="dicksonCacheTime"></div>
            </div>
        `;

        dicksonSummary.innerHTML = html;

        // Update the cache time display
        this.updateDicksonCacheTime();
    }

    displayDicksonError(message) {
        const dicksonSummary = document.getElementById('dicksonSummary');
        dicksonSummary.innerHTML = `
            <div class="text-center text-danger">
                <i class="bi bi-exclamation-triangle fs-1"></i>
                <p class="mt-2">${message}</p>
            </div>
        `;
    }

    getTemperatureStatus(device) {
        if (device.last_temperature === null) return { class: 'secondary', status: 'unknown' };
        
        const temp = device.last_temperature;
        const min = device.temp_min;
        const max = device.temp_max;
        
        if (min !== null && temp < min) return { class: 'danger', status: 'low' };
        if (max !== null && temp > max) return { class: 'danger', status: 'high' };
        return { class: 'success', status: 'normal' };
    }

    getHumidityStatus(device) {
        if (device.last_humidity === null) return { class: 'secondary', status: 'unknown' };
        
        const humidity = device.last_humidity;
        
        if (humidity < 20) return { class: 'danger', status: 'critical' };
        if (humidity < 40) return { class: 'warning', status: 'low' };
        if (humidity > 50) return { class: 'warning', status: 'high' };
        return { class: 'success', status: 'normal' };
    }

    getLocationTypeName(locationType) {
        const types = {
            'factory_floor': 'Factory Floor (18-27°C)',
            'cold_storage': 'Cold Storage (4-8°C)',
            'custom': 'Custom Range'
        };
        return types[locationType] || locationType;
    }

    async loadEnvironmentalAlerts() {
        try {
            const response = await fetch('/api/environmental/alerts');
            const result = await response.json();
            
            if (result.status === 'success') {
                this.displayEnvironmentalAlerts(result.alerts);
            } else {
                console.error('Error loading alerts:', result.message);
            }
        } catch (error) {
            console.error('Error loading environmental alerts:', error);
        }
    }

    displayEnvironmentalAlerts(alerts) {
        const alertsSection = document.getElementById('environmentalAlertsSection');
        const alertsBadge = document.getElementById('environmentalAlerts');
        const alertCount = document.getElementById('alertCount');
        const alertsList = document.getElementById('alertsList');
        
        if (alerts.length === 0) {
            alertsSection.classList.add('d-none');
            alertsBadge.classList.add('d-none');
            return;
        }
        
        // Show alerts badge
        alertsBadge.classList.remove('d-none');
        alertCount.textContent = alerts.length;
        
        // Show alerts section
        alertsSection.classList.remove('d-none');
        
        // Build alerts list
        let html = '';
        alerts.forEach(alert => {
            const severityClass = alert.severity === 'alarm' ? 'danger' : 'warning';
            const severityIcon = alert.severity === 'alarm' ? 'exclamation-triangle-fill' : 'exclamation-triangle';
            
            html += `
                <div class="alert alert-${severityClass} alert-dismissible">
                    <i class="bi bi-${severityIcon} me-2"></i>
                    <strong>${alert.device_name || 'Unknown Device'}</strong> 
                    (${alert.device_location || 'Unknown Location'}): ${alert.message}
                    <button type="button" class="btn-close" onclick="environmentalMonitor.acknowledgeAlert(${alert.id})"></button>
                </div>
            `;
        });
        
        alertsList.innerHTML = html;
    }

    async acknowledgeAlert(alertId) {
        try {
            const response = await fetch(`/api/environmental/alerts/${alertId}/acknowledge`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                // Reload alerts to update display
                this.loadEnvironmentalAlerts();
            } else {
                console.error('Error acknowledging alert:', result.message);
            }
        } catch (error) {
            console.error('Error acknowledging alert:', error);
        }
    }

    updateWeatherLoadingState() {
        const loadingIndicator = document.getElementById('weatherLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = this.isWeatherLoading ? 'inline-block' : 'none';
        }
    }

    updateDicksonLoadingState() {
        const loadingIndicator = document.getElementById('dicksonLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = this.isDicksonLoading ? 'inline-block' : 'none';
        }
    }

    updateWeatherCacheTime() {
        const cacheTimeElement = document.getElementById('weatherCacheTime');
        if (!cacheTimeElement) return;

        if (this.weatherCacheData && this.weatherCacheData.cached) {
            const now = new Date();
            const cacheAge = Math.floor((now - this.weatherCacheData.cacheTime) / 1000);
            const timeAgo = this.formatTimeAgo(cacheAge);
            cacheTimeElement.innerHTML = `<small class="text-primary">Cached ${timeAgo} ago</small>`;
        } else if (this.lastWeatherUpdate) {
            const now = new Date();
            const updateAge = Math.floor((now - this.lastWeatherUpdate) / 1000);
            const timeAgo = this.formatTimeAgo(updateAge);
            cacheTimeElement.innerHTML = `<small class="text-success">Updated ${timeAgo} ago</small>`;
        } else {
            cacheTimeElement.innerHTML = '';
        }
    }

    updateDicksonCacheTime() {
        const cacheTimeElement = document.getElementById('dicksonCacheTime');
        if (!cacheTimeElement || !this.lastDicksonUpdate) return;

        const now = new Date();
        const updateAge = Math.floor((now - this.lastDicksonUpdate) / 1000);
        const timeAgo = this.formatTimeAgo(updateAge);
        cacheTimeElement.innerHTML = `<small class="text-success">Updated ${timeAgo} ago</small>`;
    }

    updateTimestamps() {
        this.updateWeatherCacheTime();
        this.updateDicksonCacheTime();
    }

    formatTimeAgo(seconds) {
        if (seconds < 60) return `${seconds}s`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
        if (seconds < 86400) return `${Math.floor(seconds / 3600)}h`;
        return `${Math.floor(seconds / 86400)}d`;
    }

    destroy() {
        if (this.weatherUpdateInterval) clearInterval(this.weatherUpdateInterval);
        if (this.dicksonUpdateInterval) clearInterval(this.dicksonUpdateInterval);
        if (this.alertsUpdateInterval) clearInterval(this.alertsUpdateInterval);
        if (this.timeUpdateInterval) clearInterval(this.timeUpdateInterval);
    }
}

// Global instance
let environmentalMonitor;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    environmentalMonitor = new EnvironmentalMonitor();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (environmentalMonitor) {
        environmentalMonitor.destroy();
    }
});
