/* styles.css */
:root {
    --primary-bg: #121212;
    --secondary-bg: #1e1e1e;
    --card-bg: #2d2d2d;
    --primary-text: #ffffff;
    --secondary-text: #b3b3b3;
    --accent-color: #1e88e5;
    --accent-hover: #1565c0;
    --error-color: #cf6679;
    --success-color: #4caf50;
    --input-bg: #3d3d3d;
    --border-color: #444444;
}

body {
    background-color: var(--primary-bg);
    color: var(--primary-text);
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

h1, h2, h3 {
    color: var(--primary-text);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header Styles */
.header {
    background-color: var(--secondary-bg);
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.proterra-logo {
    height: 40px;
    width: auto;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
    .logo-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .proterra-logo {
        height: 30px;
    }
}

/* Form Controls */
.form-group {
    margin-bottom: 1.5rem;
    width: 100%;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    box-sizing: border-box; /* Add this to fix overflow */
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(30,136,229,0.2);
}

/* Ensure select dropdown has proper styling */
select.form-control {
    background-color: var(--input-bg);
    color: var(--primary-text);
    appearance: auto; /* Ensures dropdown arrow is visible */
}

/* Style dropdown options */
select.form-control option {
    background-color: var(--secondary-bg);
    color: var(--primary-text);
}

.status-indicator.status-disconnected {
    background-color: #ff6b6b;
}

.device-card .connection-status {
    font-weight: bold;
}

.device-card button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button */
.button {
    background-color: var(--accent-color);
    color: var(--primary-text);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.button:hover {
    background-color: var(--accent-hover);
}

.button-danger {
    background-color: var(--error-color);
}

.button-secondary {
    background-color: var(--secondary-bg);
    border: 1px solid var(--accent-color);
}

.button-secondary:hover {
    background-color: var(--accent-color);
}

/* Device List */
.device-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.device-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.device-card h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-text);
}

.device-info {
    color: var(--secondary-text);
    font-size: 0.9rem;
}

/* Log Section */
.log-container {
    background-color: var(--secondary-bg);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 2rem;
    height: 300px;
    overflow-y: auto;
}

.log-entry {
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
}

.log-info {
    color: var(--accent-color);
}

.log-error {
    color: var(--error-color);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-connected {
    background-color: var(--success-color);
}

.status-disconnected {
    background-color: var(--error-color);
}

/* MQTT Status in header */
.mqtt-status {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

#mqttIndicator {
    margin-right: 5px;
}

.status-connected {
    background-color: var(--success-color);
    box-shadow: 0 0 10px 2px var(--success-color), 0 0 30px 6px rgba(76,175,80,0.3);
    animation: glow-green 2s infinite alternate;
}

.status-disconnected {
    background-color: var(--error-color);
    box-shadow: 0 0 10px 2px var(--error-color), 0 0 30px 6px rgba(207,102,121,0.2);
    animation: glow-red 2s infinite alternate;
}

@keyframes glow-green {
    from { box-shadow: 0 0 5px 0 var(--success-color); }
    to   { box-shadow: 0 0 25px 6px var(--success-color); }
}
@keyframes glow-red {
    from { box-shadow: 0 0 5px 0 var(--error-color); }
    to   { box-shadow: 0 0 25px 6px var(--error-color); }
}


/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding: 2rem 0;
}

.header-content, .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.system-info {
    display: flex;
    gap: 2rem;
    color: var(--secondary-text);
}

.toast-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

/* Dashboard */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Device card in testing mode */
.device-card-testing {
    border: 2px solid #ff9800; /* Orange border */
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
    position: relative;
}

/* Device card when offline */
.device-card-offline {
    border: 2px solid var(--error-color);
    box-shadow: 0 0 8px rgba(207, 102, 121, 0.5);
    position: relative;
}

.device-card-offline .offline-message {
    color: #000;
    background-color: var(--error-color);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-top: 8px;
}

.device-card-testing::before {
    content: "TESTING - Not publishing to MQTT";
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: #ff9800;
    color: #000;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Device buttons container */
.device-buttons {
    display: flex;
    gap: 8px;
}

