"""
Unified API routes for the HexMES Wireless Admin system.
This replaces the scattered API endpoints with a cleaner, more RESTful approach.
"""

from flask import request, jsonify
from functools import wraps
import json
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


def register_unified_api_routes(app, db_service, device_manager, mqtt_service):
    """Register all unified API routes."""

    # =============================================================================
    # Device Management Routes
    # =============================================================================

    @app.route("/api/devices", methods=["GET"])
    def get_all_devices():
        """Get all devices, optionally filtered by entity type."""
        entity_type = request.args.get("entity_type")
        try:
            devices = db_service.get_devices(entity_type)
            return jsonify(
                {"status": "success", "devices": devices, "count": len(devices)}
            )
        except Exception as e:
            logger.error(f"Error getting devices: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices", methods=["POST"])
    def add_device():
        """Add a new device."""
        try:
            data = request.get_json()
            required_fields = ["name", "entity_type", "connection_method"]

            # Validate required fields
            for field in required_fields:
                if not data.get(field):
                    return (
                        jsonify(
                            {
                                "status": "error",
                                "message": f"Missing required field: {field}",
                            }
                        ),
                        400,
                    )

            # Create the device
            device_id = db_service.add_device(
                name=data["name"],
                entity_type=data["entity_type"],
                connection_method=data["connection_method"],
                hioki_id=data.get("hioki_id"),
                ip=data.get("ip"),
                port=data.get("port"),
                config=data.get("config", {}),
            )

            # Start the device handler
            device = db_service.get_device_by_id(device_id)
            if device:
                device_manager.start_device(device)

            return jsonify(
                {
                    "status": "success",
                    "device_id": device_id,
                    "message": f'Device {data["name"]} added successfully',
                }
            )

        except Exception as e:
            logger.error(f"Error adding device: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>", methods=["GET"])
    def get_device(device_id):
        """Get a specific device."""
        try:
            device = db_service.get_device_by_id(device_id)
            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            return jsonify({"status": "success", "device": device})
        except Exception as e:
            logger.error(f"Error getting device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>", methods=["DELETE"])
    def remove_device(device_id):
        """Remove a device."""
        try:
            device = db_service.get_device_by_id(device_id)
            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            # Stop device handler first
            if (
                device["entity_type"] == "cts"
                and device_id in device_manager.cts_handlers
            ):
                device_manager.cts_handlers[device_id].stop()
                del device_manager.cts_handlers[device_id]

            # Remove from database
            success = db_service.remove_device(device_id)

            if success:
                return jsonify(
                    {
                        "status": "success",
                        "message": f'Device {device["name"]} removed successfully',
                    }
                )
            else:
                return (
                    jsonify({"status": "error", "message": "Failed to remove device"}),
                    500,
                )

        except Exception as e:
            logger.error(f"Error removing device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>/status", methods=["GET"])
    def check_device_status(device_id):
        """Check the current status of a device."""
        try:
            device = db_service.get_device_by_id(device_id)
            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            # Force a status check
            device_manager._check_device_status(device)

            # Get updated device info
            updated_device = db_service.get_device_by_id(device_id)

            return jsonify(
                {
                    "status": "success",
                    "device_status": updated_device["status"],
                    "last_update": updated_device["last_update"],
                }
            )

        except Exception as e:
            logger.error(f"Error checking device status {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>/command", methods=["POST"])
    def send_device_command(device_id):
        """Send a command to a device."""
        try:
            data = request.get_json()
            command = data.get("command")

            if not command:
                return (
                    jsonify({"status": "error", "message": "Command is required"}),
                    400,
                )

            result = device_manager.send_command_to_device(device_id, command, **data)

            if result["status"] == "success":
                return jsonify(result)
            else:
                return jsonify(result), 400

        except Exception as e:
            logger.error(f"Error sending command to device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>/trigger_reading", methods=["POST"])
    def trigger_device_reading(device_id):
        """Trigger a reading from a device."""
        try:
            result = device_manager.trigger_device_reading(device_id)

            if result["status"] == "success":
                return jsonify(result)
            else:
                return jsonify(result), 400

        except Exception as e:
            logger.error(f"Error triggering reading for device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>/restart", methods=["POST"])
    def restart_device(device_id):
        """Restart a device handler."""
        try:
            success = device_manager.restart_device_handler(device_id)

            if success:
                return jsonify(
                    {
                        "status": "success",
                        "message": "Device handler restarted successfully",
                    }
                )
            else:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": "Failed to restart device handler",
                        }
                    ),
                    500,
                )

        except Exception as e:
            logger.error(f"Error restarting device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # Results and History Routes
    # =============================================================================

    @app.route("/api/devices/<int:device_id>/results", methods=["GET"])
    def get_device_results(device_id):
        """Get recent results for a device."""
        try:
            limit = min(int(request.args.get("limit", 100)), 1000)  # Cap at 1000
            results = db_service.get_device_results(device_id, limit)

            return jsonify(
                {"status": "success", "results": results, "count": len(results)}
            )

        except ValueError:
            return (
                jsonify({"status": "error", "message": "Invalid limit parameter"}),
                400,
            )
        except Exception as e:
            logger.error(f"Error getting results for device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/devices/<int:device_id>/commands", methods=["GET"])
    def get_device_commands(device_id):
        """Get recent commands for a device."""
        try:
            limit = min(int(request.args.get("limit", 100)), 1000)
            unprocessed_only = (
                request.args.get("unprocessed_only", "false").lower() == "true"
            )

            commands = db_service.get_recent_commands(
                device_id=device_id, unprocessed_only=unprocessed_only, limit=limit
            )

            return jsonify(
                {"status": "success", "commands": commands, "count": len(commands)}
            )

        except ValueError:
            return (
                jsonify({"status": "error", "message": "Invalid limit parameter"}),
                400,
            )
        except Exception as e:
            logger.error(f"Error getting commands for device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # W610 Specific Routes
    # =============================================================================

    @app.route("/api/w610/server_status", methods=["GET"])
    def get_w610_server_status():
        """Get W610 server status and connected devices."""
        try:
            status = device_manager.get_connected_w610_devices()
            return jsonify(status)
        except Exception as e:
            logger.error(f"Error getting W610 server status: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/w610/connected_devices", methods=["GET"])
    def get_w610_connected_devices():
        """Get W610 connected devices (alias for server_status)."""
        try:
            status = device_manager.get_connected_w610_devices()
            return jsonify(status)
        except Exception as e:
            logger.error(f"Error getting W610 connected devices: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/w610/<hioki_id>/connection_status", methods=["GET"])
    def get_w610_device_connection_status(hioki_id):
        """Get detailed connection status for a W610 device."""
        try:
            status_info = device_manager.get_connected_w610_devices()

            if status_info["server_status"] != "running":
                return jsonify(
                    {"status": "disconnected", "message": "W610 server is not running"}
                )

            connected_devices = status_info.get("devices", {})
            device_info = connected_devices.get(hioki_id)

            if device_info:
                return jsonify({"status": "connected", "device_info": device_info})
            else:
                return jsonify(
                    {
                        "status": "disconnected",
                        "message": f"Device {hioki_id} is not currently connected",
                    }
                )

        except Exception as e:
            logger.error(f"Error getting W610 connection status for {hioki_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/w610/<device_identifier>/check_status", methods=["GET"])
    def check_w610_device_status(device_identifier):
        """Check status of a specific W610 device."""
        try:
            # Try to find device by hioki_id first, then by database ID
            device = db_service.get_device_by_hioki_id(device_identifier)
            if not device and device_identifier.isdigit():
                # If not found and identifier is numeric, try database ID
                device = db_service.get_device_by_id(int(device_identifier))

            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            # Force a status check
            device_manager._check_device_status(device)

            # Get updated device info
            hioki_id = device.get("hioki_id")
            if hioki_id:
                updated_device = db_service.get_device_by_hioki_id(hioki_id)
            else:
                updated_device = db_service.get_device_by_id(device["id"])

            return jsonify({"status": updated_device["status"]})

        except Exception as e:
            logger.error(f"Error checking W610 device status {device_identifier}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/w610/<hioki_id>/send_command", methods=["POST"])
    def send_w610_command(hioki_id):
        """Send command to a W610 device."""
        try:
            data = request.get_json()
            command = data.get("command")

            if not command:
                return (
                    jsonify({"status": "error", "message": "Command is required"}),
                    400,
                )

            if (
                not device_manager.w610_server
                or not device_manager.w610_server.is_alive()
            ):
                return (
                    jsonify(
                        {"status": "error", "message": "W610 server is not running"}
                    ),
                    503,
                )

            response = device_manager.w610_server.send_command_to_device(
                hioki_id, command
            )

            if response is not None:
                return jsonify({"status": "success", "response": response})
            else:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": "No response from device or device not connected",
                        }
                    ),
                    404,
                )

        except Exception as e:
            logger.error(f"Error sending command to W610 device {hioki_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/w610/<device_id>/remove", methods=["POST"])
    def remove_w610_device(device_id):
        """Remove a W610 device."""
        try:
            # Convert device_id to int if it's numeric, otherwise treat as hioki_id
            try:
                device_id_int = int(device_id)
                device = db_service.get_device_by_id(device_id_int)
            except ValueError:
                device = db_service.get_device_by_hioki_id(device_id)

            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            db_service.delete_device(device["id"])
            return jsonify(
                {"status": "success", "message": "Device removed successfully"}
            )

        except Exception as e:
            logger.error(f"Error removing W610 device {device_id}: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # Statistics and Analytics Routes
    # =============================================================================

    @app.route("/api/statistics/devices", methods=["GET"])
    def get_device_statistics():
        """Get device statistics by type and status."""
        try:
            devices = db_service.get_devices()

            stats = {
                "total": len(devices),
                "by_entity_type": {},
                "by_status": {},
                "by_connection_method": {},
            }

            for device in devices:
                entity_type = device["entity_type"]
                status = device.get("status", "unknown")
                connection_method = device.get("connection_method", "unknown")

                # Count by entity type
                stats["by_entity_type"][entity_type] = (
                    stats["by_entity_type"].get(entity_type, 0) + 1
                )

                # Count by status
                stats["by_status"][status] = stats["by_status"].get(status, 0) + 1

                # Count by connection method
                stats["by_connection_method"][connection_method] = (
                    stats["by_connection_method"].get(connection_method, 0) + 1
                )

            return jsonify({"status": "success", "statistics": stats})

        except Exception as e:
            logger.error(f"Error getting device statistics: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/statistics/results", methods=["GET"])
    def get_results_statistics():
        """Get results statistics."""
        try:
            # This would require additional database queries
            # Implementation depends on specific analytics needs
            return jsonify(
                {
                    "status": "success",
                    "message": "Results statistics not yet implemented",
                }
            )

        except Exception as e:
            logger.error(f"Error getting results statistics: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # Legacy Compatibility Routes
    # =============================================================================

    @app.route("/api/hioki/devices", methods=["GET"])
    def get_hioki_devices_legacy():
        """Legacy endpoint for Hioki devices."""
        try:
            devices = db_service.get_devices("hioki")
            return jsonify(devices)
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route("/api/cts/devices", methods=["GET"])
    def get_cts_devices_legacy():
        """Legacy endpoint for CTS devices."""
        try:
            devices = db_service.get_devices("cts")
            return jsonify(devices)
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route("/api/ultrasonic/devices", methods=["GET"])
    def get_ultrasonic_devices_legacy():
        """Legacy endpoint for ultrasonic devices."""
        try:
            devices = db_service.get_devices("ultrasonic")
            return jsonify(devices)
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route("/api/ultrasonic/<int:welder_id>/check_status", methods=["GET"])
    def check_ultrasonic_status(welder_id):
        """Check ultrasonic welder status via HTTP."""
        try:
            device = db_service.get_device_by_id(welder_id)
            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            # Try to connect to the welder's HTTP API
            import requests

            ip = device.get("ip")
            port = device.get("port", 8080)

            try:
                response = requests.get(f"http://{ip}:{port}/status", timeout=5)
                if response.status_code == 200:
                    # Update device status to online
                    db_service.update_device_status(welder_id, "online")
                    return jsonify({"status": "online", "response": response.json()})
                else:
                    db_service.update_device_status(welder_id, "offline")
                    return jsonify(
                        {"status": "offline", "message": f"HTTP {response.status_code}"}
                    )
            except requests.RequestException as e:
                db_service.update_device_status(welder_id, "offline")
                return jsonify({"status": "offline", "message": str(e)})

        except Exception as e:
            logger.error(f"Error checking ultrasonic status: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/ultrasonic/<int:welder_id>/retry_mqtt", methods=["POST"])
    def retry_ultrasonic_mqtt(welder_id):
        """Retry MQTT publish for ultrasonic welder."""
        try:
            device = db_service.get_device_by_id(welder_id)
            if not device:
                return jsonify({"status": "error", "message": "Device not found"}), 404

            # Try to trigger MQTT retry on the welder
            import requests

            ip = device.get("ip")
            port = device.get("port", 8080)

            try:
                response = requests.post(f"http://{ip}:{port}/retry_mqtt", timeout=5)
                if response.status_code == 200:
                    return jsonify(
                        {"status": "success", "message": "MQTT retry triggered"}
                    )
                else:
                    return jsonify(
                        {"status": "error", "message": f"HTTP {response.status_code}"}
                    )
            except requests.RequestException as e:
                return jsonify({"status": "error", "message": str(e)})

        except Exception as e:
            logger.error(f"Error retrying ultrasonic MQTT: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/ultrasonic/<int:welder_id>/raw_data", methods=["GET"])
    def get_ultrasonic_raw_data(welder_id):
        """Get raw data for ultrasonic welder."""
        try:
            device = db_service.get_device_by_id(welder_id)
            if not device:
                return jsonify({"error": "Device not found"}), 404

            config = json.loads(device.get("config", "{}"))
            welder_name = config.get("welder_name", device.get("name"))

            return jsonify(
                {
                    "welder_name": welder_name,
                    "raw_data": device.get("last_raw_data", "No data available"),
                }
            )

        except Exception as e:
            logger.error(f"Error getting ultrasonic raw data: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/ultrasonic/<int:welder_id>/remove", methods=["POST"])
    def remove_ultrasonic_device(welder_id):
        """Remove ultrasonic welder device."""
        try:
            success = db_service.remove_device(welder_id)
            if success:
                # Stop device in device manager if available
                if device_manager:
                    device_manager.stop_device(welder_id)
                return jsonify({"status": "success", "message": "Device removed"})
            else:
                return jsonify({"status": "error", "message": "Device not found"}), 404

        except Exception as e:
            logger.error(f"Error removing ultrasonic device: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # Environmental Monitoring Routes
    # =============================================================================

    @app.route("/api/environmental/weather", methods=["GET"])
    def get_weather_data():
        """Get current weather data for Greer, SC."""
        try:
            from .environmental_service import environmental_service

            weather_data = environmental_service.get_weather_data()

            if weather_data:
                if weather_data.get("error"):
                    return jsonify(
                        {
                            "status": "error",
                            "message": weather_data["message"],
                            "data": weather_data,
                        }
                    )
                else:
                    return jsonify({"status": "success", "data": weather_data})
            else:
                return (
                    jsonify({"status": "error", "message": "Weather data unavailable"}),
                    503,
                )

        except Exception as e:
            logger.error(f"Error getting weather data: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/environmental/dickson", methods=["GET"])
    def get_dickson_devices():
        """Get all Dickson devices."""
        try:
            from .environmental_service import environmental_service

            devices = environmental_service.get_dickson_devices()
            return jsonify({"status": "success", "devices": devices})

        except Exception as e:
            logger.error(f"Error getting Dickson devices: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/environmental/dickson", methods=["POST"])
    def add_dickson_device():
        """Add a new Dickson device."""
        try:
            from .environmental_service import environmental_service

            data = request.get_json()

            name = data.get("name")
            location = data.get("location")
            ip_address = data.get("ip_address")
            location_type = data.get("location_type", "factory_floor")
            temp_min = data.get("temp_min")
            temp_max = data.get("temp_max")

            if not all([name, location, ip_address]):
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": "Name, location, and IP address are required",
                        }
                    ),
                    400,
                )

            device_id = environmental_service.add_dickson_device(
                name=name,
                location=location,
                ip_address=ip_address,
                location_type=location_type,
                temp_min=temp_min,
                temp_max=temp_max,
            )

            return jsonify(
                {
                    "status": "success",
                    "device_id": device_id,
                    "message": "Dickson device added successfully",
                }
            )

        except Exception as e:
            logger.error(f"Error adding Dickson device: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/environmental/dickson/<int:device_id>/reading", methods=["GET"])
    def get_dickson_reading(device_id):
        """Get current reading from a Dickson device."""
        try:
            from .environmental_service import environmental_service

            reading = environmental_service.get_dickson_reading(device_id)

            if reading:
                return jsonify({"status": "success", "reading": reading})
            else:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": "Device not found or reading failed",
                        }
                    ),
                    404,
                )

        except Exception as e:
            logger.error(f"Error getting Dickson reading: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/environmental/alerts", methods=["GET"])
    def get_environmental_alerts():
        """Get all active environmental alerts."""
        try:
            from .environmental_service import environmental_service

            alerts = environmental_service.get_active_alerts()
            return jsonify({"status": "success", "alerts": alerts})

        except Exception as e:
            logger.error(f"Error getting environmental alerts: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    @app.route("/api/environmental/alerts/<int:alert_id>/acknowledge", methods=["POST"])
    def acknowledge_environmental_alert(alert_id):
        """Acknowledge an environmental alert."""
        try:
            from .environmental_service import environmental_service

            success = environmental_service.acknowledge_alert(alert_id)

            if success:
                return jsonify({"status": "success", "message": "Alert acknowledged"})
            else:
                return (
                    jsonify(
                        {"status": "error", "message": "Failed to acknowledge alert"}
                    ),
                    500,
                )

        except Exception as e:
            logger.error(f"Error acknowledging alert: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500

    # =============================================================================
    # Utility Routes
    # =============================================================================

    @app.route("/api/system/health", methods=["GET"])
    def system_health():
        """System health check."""
        try:
            device_count = len(db_service.get_devices())
            w610_status = device_manager.get_connected_w610_devices()

            health = {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "services": {
                    "database": "online",
                    "mqtt": "online" if mqtt_service.mqtt_client else "offline",
                    "w610_server": w610_status["server_status"],
                },
                "metrics": {
                    "total_devices": device_count,
                    "w610_connected": len(w610_status.get("devices", {})),
                },
            }

            return jsonify(health)

        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return jsonify({"status": "unhealthy", "error": str(e)}), 500
