import threading
import time
import logging
import socket
import requests
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from ..utils.w610_server import W610Server
from .result_processors import ResultProcessorFactory

logger = logging.getLogger(__name__)


class UnifiedDeviceManager:
    """Manages all device types in a unified way."""

    def __init__(self, db_service, mqtt_service, socketio=None):
        self.db_service = db_service
        self.mqtt_service = mqtt_service
        self.socketio = socketio
        self.stop_event = threading.Event()

        # Active device handlers
        self.cts_handlers = {}  # device_id -> CTSTester
        self.w610_server = None
        self.background_threads = []

        # Status check managers
        self.com_manager = COMManager()

    def start_all_devices(self):
        """Start handlers for all configured devices."""
        # Always ensure W610 server is running to accept early connections
        self._ensure_w610_server()

        devices = self.db_service.get_devices()

        for device in devices:
            self.start_device(device)

        # Start background status checker
        status_thread = threading.Thread(target=self._status_checker_loop, daemon=True)
        status_thread.start()
        self.background_threads.append(status_thread)

        logger.info(f"Started unified device manager for {len(devices)} devices")

    def start_device(self, device: Dict):
        """Start a specific device based on its type."""
        entity_type = device["entity_type"]
        device_id = device["id"]

        try:
            if entity_type == "cts":
                self._start_cts_device(device)
            elif entity_type == "hioki" and device["connection_method"] == "w610":
                self._ensure_w610_server()
                if self.w610_server:
                    self.w610_server.register_device(device)
            elif entity_type == "ultrasonic":
                self._start_ultrasonic_monitoring(device)

            logger.info(f"Started {entity_type} device: {device['name']}")

        except Exception as e:
            logger.error(f"Failed to start device {device['name']}: {e}")

    def _start_cts_device(self, device: Dict):
        """Start a CTS tester handler."""
        device_id = device["id"]

        # Stop existing handler if it exists
        if device_id in self.cts_handlers:
            self.cts_handlers[device_id].stop()
            del self.cts_handlers[device_id]

        # Create CTS info dictionary for the handler
        cts_info = {
            "id": device_id,
            "name": device["name"],
            "ip": device["ip"],
            "port": device["port"],
            "cts_type": device.get("config", {}).get("cts_type", "manifold"),
            "cts_id": device.get("hioki_id", device["name"]),
        }

        # Start CTS handler
        handler = CTSTester(
            cts_info,
            (
                self.mqtt_service.mqtt_client
                if hasattr(self.mqtt_service, "mqtt_client")
                else None
            ),
            self.db_service.DB_PATH,
            self.stop_event,
        )
        handler.start()
        self.cts_handlers[device_id] = handler

    def _ensure_w610_server(self):
        """Ensure W610 server is running."""
        if not self.w610_server or not self.w610_server.is_alive():
            try:
                if self.w610_server:
                    self.w610_server.stop()
                host = os.environ.get("W610_SERVER_HOST", "0.0.0.0")
                port = int(os.environ.get("W610_SERVER_PORT", "15000"))
                self.w610_server = W610Server(self.stop_event, host, port)
                self.w610_server.start()
                logger.info(f"Started W610 server on {host}:{port}")

            except Exception as e:
                logger.error(f"Failed to start W610 server: {e}")

    def _start_ultrasonic_monitoring(self, device: Dict):
        """Start monitoring for an ultrasonic welder."""
        # Ultrasonic welders are typically monitored via HTTP polling
        # This will be handled by the status checker loop
        pass

    def stop_all_devices(self):
        """Stop all device handlers."""
        self.stop_event.set()

        # Stop CTS handlers
        for handler in self.cts_handlers.values():
            handler.stop()
        self.cts_handlers.clear()

        # Stop W610 server
        if self.w610_server:
            self.w610_server.stop()
            self.w610_server = None

        # Stop background threads
        for thread in self.background_threads:
            if thread.is_alive():
                thread.join(timeout=2.0)

        logger.info("Stopped unified device manager")

    def _status_checker_loop(self):
        """Background loop to check device status."""
        while not self.stop_event.is_set():
            try:
                self._check_all_device_status()
                self.stop_event.wait(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in status checker loop: {e}")
                self.stop_event.wait(5)  # Wait a bit before retrying

    def _check_all_device_status(self):
        """Check status of all devices."""
        devices = self.db_service.get_devices()

        for device in devices:
            try:
                self._check_device_status(device)
            except Exception as e:
                logger.error(f"Error checking status for device {device['name']}: {e}")

    def _check_device_status(self, device: Dict):
        """Check status of a specific device."""
        entity_type = device["entity_type"]
        device_id = device["id"]

        current_status = device["status"]
        new_status = "unknown"

        try:
            if entity_type == "cts":
                new_status = self._check_cts_status(device)
            elif entity_type == "hioki":
                if device["connection_method"] == "w610":
                    new_status = self._check_w610_device_status(device)
                elif device["connection_method"] == "telnet":
                    new_status = self._check_telnet_status(device)
            elif entity_type == "ultrasonic":
                new_status = self._check_ultrasonic_status(device)
            elif entity_type == "custom":
                new_status = self._check_custom_device_status(device)

            # Update status if changed
            if new_status != current_status:
                self.db_service.update_device_status(device_id, new_status)

                # Emit status change via websocket
                if self.socketio:
                    self.socketio.emit(
                        "device_status_update",
                        {
                            "device_id": device_id,
                            "hioki_id": device.get("hioki_id"),
                            "name": device["name"],
                            "entity_type": entity_type,
                            "status": new_status,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    )

                logger.info(
                    f"Device {device['name']} status changed: {current_status} -> {new_status}"
                )

        except Exception as e:
            logger.error(f"Error checking status for {device['name']}: {e}")

    def _check_cts_status(self, device: Dict) -> str:
        """Check CTS device status via telnet connection."""
        try:
            result = self.com_manager.check_status(device["ip"], int(device["port"]))
            return "online" if result else "offline"
        except Exception:
            return "offline"

    def _check_w610_device_status(self, device: Dict) -> str:
        """Check W610 Hioki device status."""
        if not self.w610_server or not self.w610_server.is_alive():
            return "offline"

        # Check if device is connected to W610 server
        connected_devices = self.w610_server.get_connected_devices()
        hioki_id = device.get("hioki_id")

        if hioki_id and hioki_id in connected_devices:
            return "online"

        # Check if device was recently offline but might come back
        last_update = device.get("last_update")
        if last_update:
            try:
                # Ensure both datetimes are timezone-aware
                last_dt = datetime.fromisoformat(last_update.replace("Z", "+00:00"))
                if not last_dt.tzinfo:
                    last_dt = last_dt.replace(tzinfo=timezone.utc)

                current_dt = datetime.now(timezone.utc)
                if (current_dt - last_dt).total_seconds() < 60:
                    return "online"  # Recently active
            except (ValueError, AttributeError):
                pass

        return "offline"

    def _check_telnet_status(self, device: Dict) -> str:
        """Check telnet-based device status."""
        try:
            result = self.com_manager.check_status(device["ip"], int(device["port"]))
            return "online" if result else "offline"
        except Exception:
            return "offline"

    def _check_ultrasonic_status(self, device: Dict) -> str:
        """Check ultrasonic welder status via HTTP."""
        try:
            url = f"http://{device['ip']}:{device['port']}/status"
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                # Optionally update last reading with status data
                try:
                    status_data = response.json()
                    self.db_service.update_device_last_reading(
                        device["id"], response.text
                    )
                except Exception:
                    pass
                return "online"
            else:
                return "offline"
        except Exception:
            return "offline"

    def _check_custom_device_status(self, device: Dict) -> str:
        """Check custom device status based on its configuration."""
        config = device.get("config", {})
        connection_method = device.get("connection_method", "http")

        if connection_method == "http":
            try:
                url = f"http://{device['ip']}:{device['port']}"
                response = requests.get(url, timeout=2)
                return "online" if response.status_code < 500 else "offline"
            except Exception:
                return "offline"
        elif connection_method == "telnet":
            try:
                result = self.com_manager.check_status(
                    device["ip"], int(device["port"])
                )
                return "online" if result else "offline"
            except Exception:
                return "offline"
        else:
            return "unknown"

    # =============================================================================
    # Device Control Methods
    # =============================================================================

    def send_command_to_device(self, device_id: int, command: str, **kwargs) -> Dict:
        """Send a command to a specific device."""
        device = self.db_service.get_device_by_id(device_id)
        if not device:
            return {"status": "error", "message": "Device not found"}

        entity_type = device["entity_type"]

        try:
            if entity_type == "hioki" and device["connection_method"] == "w610":
                return self._send_w610_command(device, command, **kwargs)
            elif entity_type == "ultrasonic":
                return self._send_ultrasonic_command(device, command, **kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"Commands not supported for {entity_type}",
                }

        except Exception as e:
            logger.error(f"Error sending command to device {device_id}: {e}")
            return {"status": "error", "message": str(e)}

    def _send_w610_command(self, device: Dict, command: str, **kwargs) -> Dict:
        """Send command to W610 device."""
        if not self.w610_server or not self.w610_server.is_alive():
            return {"status": "error", "message": "W610 server not running"}

        hioki_id = device.get("hioki_id")
        if not hioki_id:
            return {"status": "error", "message": "No Hioki ID configured"}

        try:
            response = self.w610_server.send_command_to_device(hioki_id, command)

            if response is None:
                return {
                    "status": "error",
                    "message": "Device not connected or command failed",
                }

            return {"status": "success", "command": command, "response": response}

        except Exception as e:
            return {"status": "error", "message": str(e)}

    def _send_ultrasonic_command(self, device: Dict, command: str, **kwargs) -> Dict:
        """Send command to ultrasonic welder."""
        try:
            url = f"http://{device['ip']}:{device['port']}/{command}"
            data = kwargs.get("data", {})

            if data:
                response = requests.post(url, json=data, timeout=5)
            else:
                response = requests.get(url, timeout=5)

            if response.status_code == 200:
                return {
                    "status": "success",
                    "command": command,
                    "response": response.text,
                }
            else:
                return {
                    "status": "error",
                    "message": f"HTTP {response.status_code}: {response.text}",
                }

        except Exception as e:
            return {"status": "error", "message": str(e)}

    def trigger_device_reading(self, device_id: int) -> Dict:
        """Trigger a reading from a device."""
        device = self.db_service.get_device_by_id(device_id)
        if not device:
            return {"status": "error", "message": "Device not found"}

        entity_type = device["entity_type"]

        if entity_type == "hioki":
            return self.send_command_to_device(device_id, "FETCh?")
        elif entity_type == "ultrasonic":
            return self.send_command_to_device(device_id, "status")
        else:
            return {
                "status": "error",
                "message": f"Manual readings not supported for {entity_type}",
            }

    def get_connected_w610_devices(self) -> Dict:
        """Get list of currently connected W610 devices."""
        if self.w610_server and self.w610_server.is_alive():
            return {
                "status": "success",
                "devices": self.w610_server.get_connected_devices(),
                "server_status": "running",
            }
        else:
            return {"status": "success", "devices": {}, "server_status": "stopped"}

    def restart_device_handler(self, device_id: int):
        """Restart the handler for a specific device."""
        device = self.db_service.get_device_by_id(device_id)
        if not device:
            return False

        try:
            # Stop existing handler
            if device["entity_type"] == "cts" and device_id in self.cts_handlers:
                self.cts_handlers[device_id].stop()
                del self.cts_handlers[device_id]

            # Restart the device
            self.start_device(device)
            return True

        except Exception as e:
            logger.error(f"Error restarting device handler for {device_id}: {e}")
            return False


# Inline utility classes to avoid import issues
class COMManager:
    """Utility class for simple TCP connectivity checks used by the dashboard."""

    def __init__(self, timeout=2):
        self.timeout = timeout

    def check_status(self, ip: str, port: int) -> bool:
        """Return ``True`` if the TCP port is reachable."""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(self.timeout)
            result = s.connect_ex((ip, int(port)))
            s.close()
            return result == 0
        except Exception as e:
            logger.error(f"COM check error for {ip}:{port} - {e}")
            return False


class CTSTester(threading.Thread):
    """Thread managing a connection to a single CTS tester."""

    def __init__(
        self,
        info: Dict[str, str],
        mqtt_client,
        db_path: str,
        stop_event: threading.Event,
    ):
        super().__init__(daemon=True)
        self.info = info
        self.mqtt_client = mqtt_client
        self.db_path = db_path
        self.stop_event = stop_event
        self.logger = logging.getLogger(__name__)
        self.telnet = None

    def run(self):
        while not self.stop_event.is_set():
            try:
                self._connect()
                self._listen()
            except Exception as e:
                self.logger.error(f"CTS {self.info.get('name')} error: {e}")
            finally:
                if self.telnet:
                    try:
                        self.telnet.close()
                    except Exception:
                        pass
                if not self.stop_event.is_set():
                    time.sleep(5)

    def _connect(self):
        import telnetlib

        self.telnet = telnetlib.Telnet(
            self.info["ip"], int(self.info["port"]), timeout=5
        )
        time.sleep(0.5)
        self.telnet.write(b"4\n")
        time.sleep(0.5)
        try:
            resp = self.telnet.read_very_eager().decode("utf-8", errors="ignore")
            if "Undefined command '4' entered" in resp:
                self.logger.info("CTS already connected to data stream")
        except Exception:
            pass

    def _listen(self):
        while not self.stop_event.is_set():
            try:
                line = self.telnet.read_until(b"\n", timeout=1)
            except EOFError:
                break
            if not line:
                continue
            text = line.decode("utf-8", errors="ignore").strip()
            if not text:
                continue
            # Use result processor for consistent CTS data processing
            processed_result = ResultProcessorFactory.process_result(
                entity_type="cts",
                device_id=self.info.get("cts_id") or self.info.get("name"),
                raw_data=text,
                device_config=self.info,
            )

            if processed_result:
                self._publish_processed(processed_result)
                self._update_db_processed(processed_result)

    def _parse_cts_line(self, line: str) -> Optional[Dict[str, str]]:
        """Parse a line of CTS output into fields."""
        if "*" in line or "x01" in line:
            return None

        parts = line.split()
        if not parts:
            return None

        def search(pattern: str) -> int:
            import re

            for idx, val in enumerate(parts):
                if re.fullmatch(pattern, val):
                    return idx
            return -1

        def contains(substr: str) -> int:
            for idx, val in enumerate(parts):
                if substr in val:
                    return idx
            return -1

        msg1_idx = search(r"dpsig")
        msg2_idx = search(r"psig")
        msg3_idx = contains("|GVB|")

        pressureloss = parts[msg1_idx - 1] if msg1_idx > 0 else None
        testpressure = parts[msg2_idx - 1] if msg2_idx > 0 else None
        serial = parts[msg3_idx] if msg3_idx != -1 else None

        return {
            "pressureloss": pressureloss,
            "testpressure": testpressure,
            "serial": serial,
            "raw": line,
        }

    def _publish_processed(self, processed_result: Dict[str, Any]):
        """Publish processed CTS result using result processor topics."""
        if not self.mqtt_client:
            return

        device_id = self.info.get("cts_id") or self.info.get("name")

        # Get MQTT topics from the processor
        processor = ResultProcessorFactory.get_processor("cts")
        topics = processor.get_mqtt_topics(device_id, self.info)

        # Publish individual values
        if processed_result.get("pressureloss") is not None:
            self.mqtt_client.publish(
                topics["pressureloss"], processed_result["pressureloss"], qos=1
            )
        if processed_result.get("testpressure") is not None:
            self.mqtt_client.publish(
                topics["testpressure"], processed_result["testpressure"], qos=1
            )
        # Publish raw line
        self.mqtt_client.publish(
            topics["test"], processed_result.get("raw_line", ""), qos=1
        )

    def _update_db_processed(self, processed_result: Dict[str, Any]):
        """Update database with processed CTS result."""
        try:
            # Store the processed result in the database
            # This would integrate with the unified database service
            device_id = self.info.get("id")
            if device_id:
                # Here we would call the database service to store the result
                # For now, we'll log it
                self.logger.debug(
                    f"CTS result for device {device_id}: {processed_result}"
                )
        except Exception as e:
            self.logger.error(f"Failed to store CTS result: {e}")

    # Legacy methods kept for compatibility
    def _publish(self, data: Dict[str, str]):
        """Legacy publish method - use _publish_processed instead."""
        prefix = (
            "nomuda/gvl/tools/GVB-CTS-Manifold"
            if self.info.get("cts_type") == "manifold"
            else "nomuda/gvl/tools/GVB-CTS-Enclosure"
        )
        base = f"{prefix}/{self.info.get('cts_id') or self.info.get('name')}"
        if not self.mqtt_client:
            return
        if data.get("pressureloss") is not None:
            self.mqtt_client.publish(
                f"{base}/result/pressureloss", data["pressureloss"], qos=1
            )
        if data.get("testpressure") is not None:
            self.mqtt_client.publish(
                f"{base}/result/testpressure", data["testpressure"], qos=1
            )
        self.mqtt_client.publish(f"{base}/result/test", data["raw"], qos=1)

    def _update_db(self, data: Dict[str, str]):
        """Legacy database update method - use _update_db_processed instead."""
        # This would typically update the database, but we'll skip for now
        # to avoid complex imports
        pass

    def stop(self):
        """Stop this CTS tester."""
        self.stop_event.set()
        if self.telnet:
            try:
                self.telnet.close()
            except Exception:
                pass
