import serial
import serial.tools.list_ports
import paho.mqtt.client as mqtt
import logging
import socket
import json
import os
from src.services.db_service import DBService

logger = logging.getLogger(__name__)


class COMService:
    def __init__(
        self,
        mqtt_broker="vf-gateway-01",
        mqtt_port=1883,
        mqtt_user="visualfactory",
        mqtt_password="Pr0terr@",
        db_path=None,
        mqtt_client=None,
        fallback_mqtt_ip="***********",
    ):
        self.mqtt_client = mqtt_client
        self.bridge_id = None
        self.running = True
        self.logger = logger
        self.fallback_mqtt_ip = fallback_mqtt_ip

        self.db_path = db_path or os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "data", "bridge.db"
        )
        self.db_service = DBService(self.db_path)
        self.bridge_id = self._get_or_create_bridge_id()

        # Only setup MQTT if no client was provided (normal case)
        if self.mqtt_client is None:
            self.setup_mqtt(
                mqtt_broker,
                mqtt_port,
                mqtt_user,
                mqtt_password,
                fallback_ip=fallback_mqtt_ip,
                client_id=self.bridge_id,
            )

        self.open_ports = {}

    def _generate_bridge_id(self):
        """Create a human-readable bridge ID based on hostname."""
        hostname = socket.gethostname().split(".")[0]
        return f"hioki-bridge-{hostname}"

    def _get_or_create_bridge_id(self):
        """Retrieve stored bridge ID or create a new one."""
        bridge_id = self.db_service.get_bridge_id()
        if not bridge_id:
            bridge_id = self._generate_bridge_id()
            self.db_service.set_bridge_id(bridge_id)
        return bridge_id

    def setup_mqtt(
        self, broker, port, username, password, fallback_ip=None, client_id=None
    ):
        """Initialize MQTT client connection with DNS/IP failover."""
        try:
            # Try to resolve DNS
            try:
                resolved_ip = socket.gethostbyname(broker)
                self.logger.info(f"Resolved MQTT broker '{broker}' to {resolved_ip}")
                connect_host = broker
            except socket.gaierror as e:
                self.logger.warning(f"DNS lookup failed for '{broker}': {e}")
                if fallback_ip:
                    self.logger.info(f"Falling back to MQTT broker IP: {fallback_ip}")
                    connect_host = fallback_ip
                else:
                    self.logger.error(
                        "No fallback IP specified. MQTT will not connect."
                    )
                    self.mqtt_client = None
                    return

            if client_id:
                self.bridge_id = client_id
            elif not self.bridge_id:
                hostname = socket.gethostname().split(".")[0]
                self.bridge_id = f"hioki-bridge-{hostname}"
            self.mqtt_client = mqtt.Client(
                client_id=self.bridge_id, clean_session=False
            )
            self.mqtt_client.on_connect = self._on_connect
            self.mqtt_client.on_disconnect = self._on_disconnect
            self.mqtt_client.username_pw_set(username, password)
            self.mqtt_client.reconnect_delay_set(min_delay=5, max_delay=30)
            self.mqtt_client.max_queued_messages_set(100)
            self.mqtt_client.will_set(
                f"nomuda/gvl/tools/GVB-Hioki-Bridge/{self.bridge_id}/status",
                payload="offline",
                qos=1,
                retain=True,
            )
            self.mqtt_client.connect(connect_host, port, keepalive=120)
            self.mqtt_client.loop_start()
            self.logger.info(f"MQTT client {self.bridge_id} initialized")
        except Exception as e:
            self.logger.error(f"MQTT setup failed: {str(e)}")
            self.mqtt_client = None  # Gracefully degrade

    def _on_connect(self, client, userdata, flags, rc, properties=None):
        if rc == 0:
            self.logger.info("Connected to MQTT broker successfully")
            client.publish(
                f"nomuda/gvl/tools/GVB-Hioki-Bridge/{self.bridge_id}/status",
                payload="online",
                qos=1,
                retain=True,
            )
        else:
            self.logger.error(f"Failed to connect to MQTT broker with code: {rc}")

    def _on_disconnect(self, client, userdata, rc):
        if rc != 0:
            self.logger.warning(
                f"Unexpected MQTT disconnection (code {rc}). Reconnecting..."
            )
        else:
            self.logger.info("MQTT client disconnected successfully")

    def publish_reading_mqtt(self, hioki_id, reading, timestamp, unit="mΩ"):
        """Publish a structured reading to MQTT broker."""
        if not self.mqtt_client:
            self.logger.warning("MQTT client is not connected!")
            return False
        try:
            topic = f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_id}/result/impedance"
            payload = json.dumps({
                "value": reading,
                "timestamp": timestamp,
                "unit": unit,
            })
            result = self.mqtt_client.publish(topic, payload, qos=1)
            if result.rc == 0:
                self.logger.info(f"Published to {topic}: {payload}")
                return True
            else:
                self.logger.error(f"Failed to publish to {topic}: rc={result.rc}")
                return False
        except Exception as e:
            self.logger.error(f"Error publishing to MQTT: {str(e)}")
            return False

    def publish_status(self, hioki_id, status, timestamp):
        """Publish online/offline/test status message."""
        if not self.mqtt_client:
            self.logger.warning("MQTT client is not connected!")
            return False
        try:
            topic = f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_id}/status"
            payload = json.dumps({"status": status, "timestamp": timestamp})
            result = self.mqtt_client.publish(topic, payload, qos=1)
            if result.rc == 0:
                self.logger.info(f"Published status to {topic}: {payload}")
                return True
            else:
                self.logger.error(
                    f"Failed to publish status to {topic}: rc={result.rc}"
                )
                return False
        except Exception as e:
            self.logger.error(f"Error publishing status to MQTT: {str(e)}")
            return False

    # Serial port methods
    def get_available_ports(self):
        """Get list of available COM ports"""
        return [port.device for port in serial.tools.list_ports.comports()]

    def list_ports(self):
        """List available COM ports (returns objects)"""
        return list(serial.tools.list_ports.comports())

    def open_serial(self, port, baudrate=9600, timeout=1):
        """Open a serial port and return the connection"""
        try:
            if port in self.open_ports:
                self.close_port(port)
            ser = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=timeout,
            )
            self.open_ports[port] = ser
            return ser
        except Exception as e:
            self.logger.error(f"Failed to open port {port}: {str(e)}")
            raise

    def close_port(self, port):
        """Close a specific port"""
        if port in self.open_ports:
            try:
                self.open_ports[port].close()
                del self.open_ports[port]
                self.logger.info(f"Closed port {port}")
            except Exception as e:
                self.logger.error(f"Error closing port {port}: {str(e)}")

    def shutdown(self):
        """Cleanup and disconnect all devices and MQTT"""
        self.running = False
        for port in list(self.open_ports.keys()):
            self.close_port(port)
        if self.mqtt_client:
            try:
                self.mqtt_client.loop_stop()
                self.mqtt_client.publish(
                    f"nomuda/gvl/tools/GVB-Hioki-Bridge/{self.bridge_id}/status",
                    payload="offline",
                    qos=1,
                    retain=True,
                )
                self.mqtt_client.disconnect()
                self.mqtt_client = None
            except Exception as e:
                self.logger.error(f"Error during MQTT shutdown: {str(e)}")
        self.logger.info("COM service shutdown complete")
