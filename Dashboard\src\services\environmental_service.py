"""
Environmental monitoring service for temperature and humidity tracking.
Handles weather data and Dickson device monitoring.
"""

import json
import logging
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import sqlite3
from .unified_db_service import unified_service

logger = logging.getLogger(__name__)

# Weather API configuration
WEATHER_API_KEY = "your_openweather_api_key"  # Replace with actual API key
GREER_LAT = 34.9262
GREER_LON = -82.2270

# Temperature ranges (in Celsius)
TEMP_RANGES = {
    "factory_floor": {"min": 18, "max": 27, "name": "Factory Floor"},
    "cold_storage": {"min": 4, "max": 8, "name": "Cold Storage"},
    "custom": {"min": None, "max": None, "name": "Custom"},
}

# Humidity ranges (in percentage)
HUMIDITY_WARNING = 40  # Below this shows yellow warning
HUMIDITY_ALARM = 20  # Below this shows red alarm
HUMIDITY_MAX = 50  # Above this shows warning


class EnvironmentalService:
    """Service for environmental monitoring and alerts."""

    def __init__(self):
        self.db_service = unified_service
        self._init_environmental_tables()

    def _init_environmental_tables(self):
        """Initialize environmental monitoring tables."""
        try:
            # Dickson devices table
            self.db_service.execute_query(
                """
                CREATE TABLE IF NOT EXISTS dickson_devices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    location TEXT NOT NULL,
                    ip_address TEXT NOT NULL,
                    location_type TEXT DEFAULT 'factory_floor',
                    temp_min REAL,
                    temp_max REAL,
                    status TEXT DEFAULT 'offline',
                    last_reading_time TEXT,
                    last_temperature REAL,
                    last_humidity REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Environmental readings table
            self.db_service.execute_query(
                """
                CREATE TABLE IF NOT EXISTS environmental_readings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id INTEGER,
                    device_type TEXT NOT NULL,  -- 'weather' or 'dickson'
                    temperature_c REAL,
                    temperature_f REAL,
                    humidity REAL,
                    location TEXT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (device_id) REFERENCES dickson_devices (id)
                )
            """
            )

            # Environmental alerts table
            self.db_service.execute_query(
                """
                CREATE TABLE IF NOT EXISTS environmental_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id INTEGER,
                    device_type TEXT NOT NULL,
                    alert_type TEXT NOT NULL,  -- 'temp_high', 'temp_low', 'humidity_low', 'humidity_high'
                    message TEXT NOT NULL,
                    severity TEXT NOT NULL,   -- 'warning', 'alarm'
                    acknowledged BOOLEAN DEFAULT FALSE,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    acknowledged_at TEXT,
                    FOREIGN KEY (device_id) REFERENCES dickson_devices (id)
                )
            """
            )

            logger.info("Environmental monitoring tables initialized")

        except Exception as e:
            logger.error(f"Error initializing environmental tables: {e}")

    def add_dickson_device(
        self,
        name: str,
        location: str,
        ip_address: str,
        location_type: str = "factory_floor",
        temp_min: float = None,
        temp_max: float = None,
    ) -> int:
        """Add a new Dickson device."""
        try:
            # Set temperature ranges based on location type
            if location_type in TEMP_RANGES and temp_min is None and temp_max is None:
                temp_min = TEMP_RANGES[location_type]["min"]
                temp_max = TEMP_RANGES[location_type]["max"]

            device_id = self.db_service.execute_query(
                """
                INSERT INTO dickson_devices 
                (name, location, ip_address, location_type, temp_min, temp_max)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (name, location, ip_address, location_type, temp_min, temp_max),
            )

            logger.info(f"Added Dickson device: {name} at {location}")
            return device_id

        except Exception as e:
            logger.error(f"Error adding Dickson device: {e}")
            raise

    def get_dickson_devices(self) -> List[Dict]:
        """Get all Dickson devices."""
        try:
            devices = self.db_service.execute_query(
                """
                SELECT * FROM dickson_devices ORDER BY name
            """,
                fetch_all=True,
            )

            return [dict(device) for device in devices] if devices else []

        except Exception as e:
            logger.error(f"Error getting Dickson devices: {e}")
            return []

    def get_weather_data(self) -> Optional[Dict]:
        """Get current weather data for Greer, SC."""
        try:
            if (
                not WEATHER_API_KEY
                or WEATHER_API_KEY == "640e721f57ae47125d636544b1bcf297"
            ):
                # Return mock data for development
                return {
                    "temperature_c": 22.5,
                    "temperature_f": 72.5,
                    "humidity": 65,
                    "description": "Partly cloudy",
                    "location": "Greer, SC",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            url = f"https://api.openweathermap.org/data/2.5/weather"
            params = {
                "lat": GREER_LAT,
                "lon": GREER_LON,
                "appid": WEATHER_API_KEY,
                "units": "metric",
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()

            temp_c = data["main"]["temp"]
            temp_f = (temp_c * 9 / 5) + 32
            humidity = data["main"]["humidity"]

            weather_data = {
                "temperature_c": round(temp_c, 1),
                "temperature_f": round(temp_f, 1),
                "humidity": humidity,
                "description": data["weather"][0]["description"].title(),
                "location": "Greer, SC",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            # Store weather reading
            self._store_environmental_reading(
                device_id=None,
                device_type="weather",
                temperature_c=temp_c,
                temperature_f=temp_f,
                humidity=humidity,
                location="Greer, SC",
            )

            return weather_data

        except Exception as e:
            logger.error(f"Error getting weather data: {e}")
            # Return error data instead of None
            return {
                "error": True,
                "message": "Connection to Weather API Unavailable",
                "location": "Greer, SC",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def get_dickson_reading(self, device_id: int) -> Optional[Dict]:
        """Get reading from a Dickson device."""
        try:
            device = self.db_service.execute_query(
                """
                SELECT * FROM dickson_devices WHERE id = ?
            """,
                (device_id,),
                fetch_one=True,
            )

            if not device:
                return None

            device = dict(device)

            # Try to get reading from Dickson device
            # This would need to be implemented based on Dickson API
            # For now, return mock data
            temp_c = 23.5  # Mock temperature
            temp_f = (temp_c * 9 / 5) + 32
            humidity = 45  # Mock humidity

            reading = {
                "device_id": device_id,
                "name": device["name"],
                "location": device["location"],
                "temperature_c": temp_c,
                "temperature_f": round(temp_f, 1),
                "humidity": humidity,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            # Update device status and last reading
            self.db_service.execute_query(
                """
                UPDATE dickson_devices 
                SET status = 'online', last_reading_time = ?, 
                    last_temperature = ?, last_humidity = ?, updated_at = ?
                WHERE id = ?
            """,
                (
                    reading["timestamp"],
                    temp_c,
                    humidity,
                    datetime.now(timezone.utc).isoformat(),
                    device_id,
                ),
            )

            # Store reading
            self._store_environmental_reading(
                device_id=device_id,
                device_type="dickson",
                temperature_c=temp_c,
                temperature_f=temp_f,
                humidity=humidity,
                location=device["location"],
            )

            # Check for alerts
            self._check_environmental_alerts(device, temp_c, humidity)

            return reading

        except Exception as e:
            logger.error(f"Error getting Dickson reading for device {device_id}: {e}")
            return None

    def _store_environmental_reading(
        self,
        device_id: Optional[int],
        device_type: str,
        temperature_c: float,
        temperature_f: float,
        humidity: float,
        location: str,
    ):
        """Store environmental reading in database."""
        try:
            self.db_service.execute_query(
                """
                INSERT INTO environmental_readings 
                (device_id, device_type, temperature_c, temperature_f, humidity, location)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (
                    device_id,
                    device_type,
                    temperature_c,
                    temperature_f,
                    humidity,
                    location,
                ),
            )

        except Exception as e:
            logger.error(f"Error storing environmental reading: {e}")

    def _check_environmental_alerts(
        self, device: Dict, temperature_c: float, humidity: float
    ):
        """Check for environmental alerts and create them if needed."""
        try:
            device_id = device["id"]
            temp_min = device.get("temp_min")
            temp_max = device.get("temp_max")

            # Clear old unacknowledged alerts for this device
            self.db_service.execute_query(
                """
                DELETE FROM environmental_alerts 
                WHERE device_id = ? AND acknowledged = FALSE
            """,
                (device_id,),
            )

            # Check temperature alerts
            if temp_min is not None and temperature_c < temp_min:
                self._create_alert(
                    device_id,
                    "dickson",
                    "temp_low",
                    f"Temperature {temperature_c}°C below minimum {temp_min}°C",
                    "alarm",
                )
            elif temp_max is not None and temperature_c > temp_max:
                self._create_alert(
                    device_id,
                    "dickson",
                    "temp_high",
                    f"Temperature {temperature_c}°C above maximum {temp_max}°C",
                    "alarm",
                )

            # Check humidity alerts
            if humidity < HUMIDITY_ALARM:
                self._create_alert(
                    device_id,
                    "dickson",
                    "humidity_low",
                    f"Humidity {humidity}% critically low (below {HUMIDITY_ALARM}%)",
                    "alarm",
                )
            elif humidity < HUMIDITY_WARNING:
                self._create_alert(
                    device_id,
                    "dickson",
                    "humidity_low",
                    f"Humidity {humidity}% below recommended {HUMIDITY_WARNING}%",
                    "warning",
                )
            elif humidity > HUMIDITY_MAX:
                self._create_alert(
                    device_id,
                    "dickson",
                    "humidity_high",
                    f"Humidity {humidity}% above recommended {HUMIDITY_MAX}%",
                    "warning",
                )

        except Exception as e:
            logger.error(f"Error checking environmental alerts: {e}")

    def _create_alert(
        self,
        device_id: int,
        device_type: str,
        alert_type: str,
        message: str,
        severity: str,
    ):
        """Create an environmental alert."""
        try:
            self.db_service.execute_query(
                """
                INSERT INTO environmental_alerts 
                (device_id, device_type, alert_type, message, severity)
                VALUES (?, ?, ?, ?, ?)
            """,
                (device_id, device_type, alert_type, message, severity),
            )

        except Exception as e:
            logger.error(f"Error creating environmental alert: {e}")

    def get_active_alerts(self) -> List[Dict]:
        """Get all active (unacknowledged) environmental alerts."""
        try:
            alerts = self.db_service.execute_query(
                """
                SELECT ea.*, dd.name as device_name, dd.location as device_location
                FROM environmental_alerts ea
                LEFT JOIN dickson_devices dd ON ea.device_id = dd.id
                WHERE ea.acknowledged = FALSE
                ORDER BY ea.created_at DESC
            """,
                fetch_all=True,
            )

            return [dict(alert) for alert in alerts] if alerts else []

        except Exception as e:
            logger.error(f"Error getting active alerts: {e}")
            return []

    def acknowledge_alert(self, alert_id: int) -> bool:
        """Acknowledge an environmental alert."""
        try:
            self.db_service.execute_query(
                """
                UPDATE environmental_alerts 
                SET acknowledged = TRUE, acknowledged_at = ?
                WHERE id = ?
            """,
                (datetime.now(timezone.utc).isoformat(), alert_id),
            )

            return True

        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {e}")
            return False


# Global service instance
environmental_service = EnvironmentalService()
