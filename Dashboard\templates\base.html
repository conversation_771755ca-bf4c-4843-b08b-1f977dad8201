<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HexMES Wireless Admin{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='favicon.png') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    {% block extra_css %}{% endblock %}
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='images/Proterra_Logo.png') }}" alt="Proterra Logo"
                    class="proterra-logo me-2">
                <img src="{{ url_for('static', filename='images/HexMES_dark.png') }}" alt="HexMES Wireless Admin"
                    class="hexmes-logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Devices</a>
                    </li>
                </ul>
                <div class="ms-auto d-flex align-items-center">
                    {% if session.username %}
                    <button class="btn btn-outline-secondary btn-sm me-2" data-bs-toggle="modal"
                        data-bs-target="#databaseSettingsModal">
                        <i class="bi bi-database"></i> Database
                    </button>
                    <span class="navbar-text me-2">{{ session.username }}</span>
                    <a class="btn btn-outline-light btn-sm" href="{{ url_for('logout') }}">Logout</a>
                    {% else %}
                    <a class="btn btn-outline-light btn-sm" href="{{ url_for('login') }}">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        {% block content %}{% endblock %}
    </div>

    <!-- Database Settings Modal -->
    {% if session.username %}
    <div class="modal fade" id="databaseSettingsModal" tabindex="-1" aria-labelledby="databaseSettingsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="databaseSettingsModalLabel">
                        <i class="bi bi-database"></i> Database Settings
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>MESDB Connection</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span>Status:</span>
                                        <span id="mesdb-status" class="badge bg-secondary">Checking...</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>Server:</strong>
                                        rds-bat-prd-use1-ignition-mssql-ee.cerffldjycr9.us-east-1.rds.amazonaws.com<br>
                                        <strong>Database:</strong> REVMES<br>
                                        <strong>Table:</strong> mes.VF_ToolData
                                    </div>
                                    <div class="mb-3">
                                        <strong>Record Count:</strong> <span id="mesdb-record-count">-</span>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button id="test-mesdb-btn" class="btn btn-primary btn-sm">
                                            <i class="bi bi-arrow-clockwise"></i> Test Connection
                                        </button>
                                        <button id="toggle-mesdb-btn" class="btn btn-warning btn-sm">
                                            <i class="bi bi-power"></i> <span id="toggle-mesdb-text">Disable</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Error Log</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div id="mesdb-error-log" class="text-muted"
                                        style="max-height: 200px; overflow-y: auto;">
                                        No errors
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <footer class="footer text-light py-3 mt-auto">
        <div class="container d-flex justify-content-between align-items-center">
            <span>HexMES Wireless Admin &copy; 2025 Proterra</span>
            <p class="version">Version: {{ version }}</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/realtime-updates.js') }}"></script>
    <script src="{{ url_for('static', filename='js/environmental.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>

</html>