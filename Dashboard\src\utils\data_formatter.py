from typing import Optional, Union
import logging
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)


def format_data(payload: str) -> Optional[str]:
    """Format Hioki reading and remove scientific notation.

    The W610 returns readings in scientific notation (e.g. ``00.81394E-03``).
    This helper converts the value to milliohms and returns a decimal string
    without an exponent so it can be safely published to MQTT and stored in
    the database.
    """

    try:
        value = Decimal(payload.strip()) * Decimal("1000")
        # Quantize to 4 decimal places and convert to plain string
        value = value.quantize(Decimal("0.0001"))
        return format(value, 'f')
    except (InvalidOperation, ValueError) as e:
        logger.error(f"Error formatting data: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in data formatting: {str(e)}")
        return None


def create_mqtt_topic(hioki_number: str) -> str:
    return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_number}/result/impedance"


def validate_hioki_number(hioki_number: str) -> bool:
    if not hioki_number:
        return False
    return len(hioki_number) == 4 and hioki_number[0] == "H" and hioki_number[1:].isdigit()


def is_valid_reading(reading) -> bool:
    try:
        value = float(reading)
        return -9999.0 < value < 9999.0
    except Exception:
        return False


def format_reading_for_display(value: Union[float, str]) -> str:
    try:
        if isinstance(value, str):
            value = float(value)
        return f"{value:.4f} mΩ"
    except (ValueError, TypeError):
        return "N/A"
