# HexMES Bridge - Proterra MES Internal Documentation

This document provides Proterra MES team members with guidelines for deploying
and supporting the HexMES Bridge application. The application interfaces
with Hioki resistance meters and exposes readings over MQTT as well as through
a web dashboard.

> **Warning:** This application is intended for EOL once <PERSON><PERSON> has wireless COM devices.

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <internal-repo-url>
   cd Bridge-Client
   ```
   

2. **Create a Python virtual environment** (Python 3.8 or newer is
   recommended)
   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**
   ```bash
   python run.py
   ```
   Access the dashboard at `http://<bridge-host>:5000`.

## Support Guidelines

- For operational issues or bug reports, create a ticket with the MES team in
  JIRA OTSM.
- The service writes logs to standard output. When running as a service, ensure
  logs are captured to assist with troubleshooting.
- The bridge ID is derived from the host name. If the host name changes, delete
  the `data/bridge.db` file so a new ID can be generated.

## Application Structure

```
Bridge-Client/
├── run.py              # Entry point for the application
├── requirements.txt    # Python dependencies
├── src/
│   ├── app.py          # Flask application factory
│   ├── routes.py       # HTTP API and web routes
│   ├── models/         # Data models
│   │   └── device.py   # Device model definition
│   ├── services/       # Backend services
│   │   ├── com_service.py  # Handles serial communication and MQTT
│   │   └── db_service.py   # Database operations
│   ├── utils/          # Helper modules
│   │   ├── device_manager.py  # Manages device connections
│   │   └── helpers.py        # Utility functions
│   ├── templates/      # Jinja2 templates for the UI
│   │   ├── base.html   # Base template with common elements
│   │   └── index.html  # Main dashboard template
│   └── static/         # Static assets
│       ├── css/        # Stylesheets
│       ├── js/         # JavaScript files
│       └── images/     # Image assets including logos
└── data/               # SQLite database storage
    └── bridge.db       # Local database file
```

This README is intended for Proterra internal use only.
