{% extends 'base.html' %}

{% block title %}HexMES Wireless Admin - Unified Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
	<div class="col">
		<h1 class="display-5">
			<img src="{{ url_for('static', filename='images/HexMES_dark.png') }}" alt="HexMES Wireless Admin"
				class="hexmes-logo-lg me-2">
		</h1>
		<p class="lead">Unified Device Management Dashboard</p>
	</div>
	<div class="col-auto d-flex align-items-center gap-2">
		<button id="refreshAllBtn" class="btn btn-outline-primary">
			<i class="bi bi-arrow-repeat me-1"></i>Refresh All
		</button>
		<div class="dropdown">
			<button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
				<i class="bi bi-plus-circle me-1"></i>Add Device
			</button>
			<ul class="dropdown-menu">
				<li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addDeviceModal"
						data-entity-type="hioki">
						<i class="bi bi-lightning me-2"></i>Hioki Device</a></li>
				<li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addDeviceModal"
						data-entity-type="cts">
						<i class="bi bi-droplet me-2"></i>CTS Tester</a></li>
				<li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addDeviceModal"
						data-entity-type="ultrasonic">
						<i class="bi bi-soundwave me-2"></i>Ultrasonic Welder</a></li>
				<li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addDeviceModal"
						data-entity-type="custom">
						<i class="bi bi-gear me-2"></i>Custom Device</a></li>
			</ul>
		</div>
	</div>
</div>

<!-- Statistics Row -->
<div class="row mb-4">
	<div class="col-md-3">
		<div class="card text-center">
			<div class="card-body">
				<h5 class="card-title text-primary">Total Devices</h5>
				<h2 id="totalDevicesCount" class="mb-0">{{ devices|length }}</h2>
			</div>
		</div>
	</div>
	<div class="col-md-3">
		<div class="card text-center">
			<div class="card-body">
				<h5 class="card-title text-success">Online</h5>
				<h2 id="onlineDevicesCount" class="mb-0">{{ devices|selectattr('status', 'equalto',
					'online')|list|length }}</h2>
			</div>
		</div>
	</div>
	<div class="col-md-3">
		<div class="card text-center">
			<div class="card-body">
				<h5 class="card-title text-danger">Offline</h5>
				<h2 id="offlineDevicesCount" class="mb-0">{{ devices|selectattr('status', 'equalto',
					'offline')|list|length }}</h2>
			</div>
		</div>
	</div>
	<div class="col-md-3">
		<div class="card text-center">
			<div class="card-body">
				<h5 class="card-title text-info">W610 Connected</h5>
				<h2 id="w610ConnectedCount" class="mb-0">0</h2>
			</div>
		</div>
	</div>
</div>

<!-- Filter Row -->
<div class="row mb-3">
	<div class="col-md-4">
		<select id="entityTypeFilter" class="form-select">
			<option value="">All Device Types</option>
			<option value="hioki">Hioki Devices</option>
			<option value="cts">CTS Testers</option>
			<option value="ultrasonic">Ultrasonic Welders</option>
			<option value="custom">Custom Devices</option>
		</select>
	</div>
	<div class="col-md-4">
		<select id="statusFilter" class="form-select">
			<option value="">All Statuses</option>
			<option value="online">Online</option>
			<option value="offline">Offline</option>
			<option value="unknown">Unknown</option>
		</select>
	</div>
	<div class="col-md-4">
		<input type="text" id="searchFilter" class="form-control" placeholder="Search devices...">
	</div>
</div>

<!-- Devices Table -->
<div class="row">
	<div class="col-12">
		<div class="card">
			<div class="card-header">
				<h5 class="mb-0">All Devices</h5>
			</div>
			<div class="card-body">
				<div class="table-responsive">
					<table class="table table-hover" id="devicesTable">
						<thead>
							<tr>
								<th>Name</th>
								<th>Type</th>
								<th>ID/Address</th>
								<th>Connection</th>
								<th>Status</th>
								<th>Last Reading</th>
								<th>Last Update</th>
								<th>Actions</th>
							</tr>
						</thead>
						<tbody id="devicesTableBody">
							{% for device in devices %}
							<tr data-device-id="{{ device.id }}" data-entity-type="{{ device.entity_type }}"
								data-status="{{ device.status }}" data-hioki-id="{{ device.hioki_id or '' }}">
								<td>
									<strong>{{ device.name }}</strong>
								</td>
								<td>
									<span class="badge bg-info">
										{% if device.entity_type == 'hioki' %}
										<i class="bi bi-lightning me-1"></i>Hioki
										{% elif device.entity_type == 'cts' %}
										<i class="bi bi-droplet me-1"></i>CTS
										{% elif device.entity_type == 'ultrasonic' %}
										<i class="bi bi-soundwave me-1"></i>Ultrasonic
										{% else %}
										<i class="bi bi-gear me-1"></i>{{ device.entity_type.title() }}
										{% endif %}
									</span>
								</td>
								<td>
									{% if device.hioki_id %}
									{{ device.hioki_id }}
									{% elif device.ip %}
									{{ device.ip }}{% if device.port %}:{{ device.port }}{% endif %}
									{% else %}
									N/A
									{% endif %}
								</td>
								<td>
									<small class="text-muted">{{ device.connection_method or 'Unknown' }}</small>
								</td>
								<td>
									<span class="badge device-status-badge 
                                        {% if device.status == 'online' %}bg-success
                                        {% elif device.status == 'offline' %}bg-danger
                                        {% else %}bg-secondary{% endif %}">
										{{ device.status or 'Unknown' }}
									</span>
								</td>
								<td class="device-reading">
									{{ device.last_reading or 'N/A' }}
								</td>
								<td class="device-timestamp">
									{% if device.last_update %}
									<span class="last-update-time">{{ device.last_update }}</span>
									{% else %}
									Never
									{% endif %}
								</td>
								<td>
									<div class="btn-group btn-group-sm" role="group">
										<button class="btn btn-outline-primary check-status-btn"
											data-device-id="{{ device.id }}" title="Check Status">
											<i class="bi bi-arrow-repeat"></i>
										</button>
										{% if device.entity_type in ['hioki', 'ultrasonic'] %}
										<button class="btn btn-outline-success trigger-reading-btn"
											data-device-id="{{ device.id }}" title="Trigger Reading">
											<i class="bi bi-play-fill"></i>
										</button>
										{% endif %}
										<button class="btn btn-outline-info view-details-btn"
											data-device-id="{{ device.id }}" title="View Details">
											<i class="bi bi-info-circle"></i>
										</button>
										<button class="btn btn-outline-danger remove-device-btn"
											data-device-id="{{ device.id }}" data-device-name="{{ device.name }}"
											title="Remove Device">
											<i class="bi bi-trash"></i>
										</button>
									</div>
								</td>
							</tr>
							{% endfor %}
						</tbody>
					</table>
				</div>
				{% if not devices %}
				<div class="alert alert-info">
					<i class="bi bi-info-circle me-2"></i>No devices have been added yet.
					Use the "Add Device" button to add your first device.
				</div>
				{% endif %}
			</div>
		</div>
	</div>
</div>

<!-- Add Device Modal -->
<div class="modal fade" id="addDeviceModal" tabindex="-1">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Add New Device</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<form id="addDeviceForm">
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="deviceName" class="form-label">Device Name *</label>
							<input type="text" class="form-control" id="deviceName" required>
						</div>
						<div class="col-md-6">
							<label for="entityType" class="form-label">Device Type *</label>
							<select class="form-select" id="entityType" required>
								<option value="">Select Type</option>
								<option value="hioki">Hioki Device</option>
								<option value="cts">CTS Tester</option>
								<option value="ultrasonic">Ultrasonic Welder</option>
								<option value="custom">Custom Device</option>
							</select>
						</div>
					</div>

					<!-- Hioki-specific fields -->
					<div id="hiokiFields" class="entity-specific-fields d-none">
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="hiokiId" class="form-label">Hioki ID *</label>
								<input type="text" class="form-control" id="hiokiId" placeholder="e.g., H001">
							</div>
							<div class="col-md-6">
								<label for="hiokiConnection" class="form-label">Connection Method *</label>
								<select class="form-select" id="hiokiConnection">
									<option value="w610">W610 Network Module</option>
									<option value="telnet">Direct Telnet</option>
								</select>
							</div>
						</div>
						<div class="row mb-3" id="hiokiNetworkFields">
							<div class="col-md-6">
								<label for="hiokiIp" class="form-label">IP Address</label>
								<input type="text" class="form-control" id="hiokiIp" placeholder="*************">
								<div class="form-text">For W610: Device's network IP. For Telnet: Device IP.</div>
							</div>
							<div class="col-md-6">
								<label for="hiokiPort" class="form-label">Port</label>
								<input type="number" class="form-control" id="hiokiPort" value="15000">
								<div class="form-text">Default: 15000 for W610, 23 for Telnet</div>
							</div>
						</div>
					</div>

					<!-- CTS-specific fields -->
					<div id="ctsFields" class="entity-specific-fields d-none">
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="ctsIp" class="form-label">IP Address *</label>
								<input type="text" class="form-control" id="ctsIp" placeholder="*************">
							</div>
							<div class="col-md-6">
								<label for="ctsPort" class="form-label">Port</label>
								<input type="number" class="form-control" id="ctsPort" value="23">
							</div>
						</div>
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="ctsId" class="form-label">CTS ID</label>
								<input type="text" class="form-control" id="ctsId" placeholder="L1M2">
							</div>
							<div class="col-md-6">
								<label for="ctsType" class="form-label">CTS Type</label>
								<select class="form-select" id="ctsType">
									<option value="manifold">Manifold</option>
									<option value="enclosure">Enclosure</option>
								</select>
							</div>
						</div>
					</div>

					<!-- Ultrasonic-specific fields -->
					<div id="ultrasonicFields" class="entity-specific-fields d-none">
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="ultrasonicIp" class="form-label">IP Address *</label>
								<input type="text" class="form-control" id="ultrasonicIp" placeholder="*************">
							</div>
							<div class="col-md-6">
								<label for="ultrasonicPort" class="form-label">Port</label>
								<input type="number" class="form-control" id="ultrasonicPort" value="8080">
							</div>
						</div>
						<div class="mb-3">
							<label for="ultrasonicRaspiName" class="form-label">Raspberry Pi Name</label>
							<input type="text" class="form-control" id="ultrasonicRaspiName"
								placeholder="Line1-Station3">
						</div>
					</div>

					<!-- Custom device fields -->
					<div id="customFields" class="entity-specific-fields d-none">
						<div class="row mb-3">
							<div class="col-md-6">
								<label for="customIp" class="form-label">IP Address *</label>
								<input type="text" class="form-control" id="customIp" placeholder="*************">
							</div>
							<div class="col-md-6">
								<label for="customPort" class="form-label">Port</label>
								<input type="number" class="form-control" id="customPort" value="80">
							</div>
						</div>
						<div class="mb-3">
							<label for="customConnection" class="form-label">Connection Method</label>
							<select class="form-select" id="customConnection">
								<option value="http">HTTP</option>
								<option value="telnet">Telnet</option>
								<option value="mqtt">MQTT</option>
							</select>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary" id="saveDeviceBtn">Add Device</button>
			</div>
		</div>
	</div>
</div>

<!-- Device Details Modal -->
<div class="modal fade" id="deviceDetailsModal" tabindex="-1">
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Device Details</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<div id="deviceDetailsContent">
					<div class="text-center">
						<div class="spinner-border" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<!-- Remove Device Modal -->
<div class="modal fade" id="removeDeviceModal" tabindex="-1">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Remove Device</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<p>Are you sure you want to remove device <strong id="removeDeviceName"></strong>?</p>
				<p class="text-danger">This will remove all associated data and cannot be undone.</p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-danger" id="confirmRemoveDevice">Remove</button>
			</div>
		</div>
	</div>
</div>

{% endblock %}

{% block extra_js %}
<script>
	// Global variables
	let currentDeviceId = null;

	// Initialize page
	document.addEventListener('DOMContentLoaded', function () {
		initializeEventListeners();
		loadW610Status();
		formatTimestamps();

		// Set up periodic refresh
		setInterval(refreshDeviceData, 10000); // Every 10 seconds
	});

	function initializeEventListeners() {
		// Add device modal
		document.getElementById('entityType').addEventListener('change', handleEntityTypeChange);
		document.getElementById('hiokiConnection').addEventListener('change', handleHiokiConnectionChange);
		document.getElementById('saveDeviceBtn').addEventListener('click', handleAddDevice);

		// Device actions
		document.querySelectorAll('.check-status-btn').forEach(btn => {
			btn.addEventListener('click', function () {
				checkDeviceStatus(this.getAttribute('data-device-id'));
			});
		});

		document.querySelectorAll('.trigger-reading-btn').forEach(btn => {
			btn.addEventListener('click', function () {
				triggerDeviceReading(this.getAttribute('data-device-id'));
			});
		});

		document.querySelectorAll('.view-details-btn').forEach(btn => {
			btn.addEventListener('click', function () {
				showDeviceDetails(this.getAttribute('data-device-id'));
			});
		});

		document.querySelectorAll('.remove-device-btn').forEach(btn => {
			btn.addEventListener('click', function () {
				showRemoveDeviceModal(
					this.getAttribute('data-device-id'),
					this.getAttribute('data-device-name')
				);
			});
		});

		// Filters
		document.getElementById('entityTypeFilter').addEventListener('change', applyFilters);
		document.getElementById('statusFilter').addEventListener('change', applyFilters);
		document.getElementById('searchFilter').addEventListener('input', applyFilters);

		// Other buttons
		document.getElementById('refreshAllBtn').addEventListener('click', refreshAllDevices);
		document.getElementById('confirmRemoveDevice').addEventListener('click', confirmRemoveDevice);
	}

	// Modal handling
	function handleEntityTypeChange() {
		const entityType = this.value;
		document.querySelectorAll('.entity-specific-fields').forEach(el => el.classList.add('d-none'));

		if (entityType) {
			const fieldsId = entityType + 'Fields';
			document.getElementById(fieldsId)?.classList.remove('d-none');
		}

		// Set default connection method for Hioki
		if (entityType === 'hioki') {
			handleHiokiConnectionChange();
		}
	}

	function handleHiokiConnectionChange() {
		const connection = document.getElementById('hiokiConnection').value;
		const portField = document.getElementById('hiokiPort');

		if (connection === 'w610') {
			portField.value = '15000';
		} else if (connection === 'telnet') {
			portField.value = '23';
		}
	}

	// Device management functions
	function handleAddDevice() {
		const entityType = document.getElementById('entityType').value;
		const deviceName = document.getElementById('deviceName').value.trim();

		if (!entityType || !deviceName) {
			alert('Please fill in all required fields');
			return;
		}

		const deviceData = {
			name: deviceName,
			entity_type: entityType
		};

		// Collect entity-specific data
		if (entityType === 'hioki') {
			deviceData.hioki_id = document.getElementById('hiokiId').value.trim();
			deviceData.connection_method = document.getElementById('hiokiConnection').value;
			deviceData.ip = document.getElementById('hiokiIp').value.trim();
			deviceData.port = document.getElementById('hiokiPort').value.trim();

			if (!deviceData.hioki_id) {
				alert('Hioki ID is required');
				return;
			}
		} else if (entityType === 'cts') {
			deviceData.connection_method = 'telnet';
			deviceData.ip = document.getElementById('ctsIp').value.trim();
			deviceData.port = document.getElementById('ctsPort').value.trim();
			deviceData.hioki_id = document.getElementById('ctsId').value.trim();
			deviceData.config = {
				cts_type: document.getElementById('ctsType').value
			};

			if (!deviceData.ip) {
				alert('IP Address is required for CTS devices');
				return;
			}
		} else if (entityType === 'ultrasonic') {
			deviceData.connection_method = 'http';
			deviceData.ip = document.getElementById('ultrasonicIp').value.trim();
			deviceData.port = document.getElementById('ultrasonicPort').value.trim();
			deviceData.config = {
				raspi_name: document.getElementById('ultrasonicRaspiName').value.trim()
			};

			if (!deviceData.ip) {
				alert('IP Address is required for Ultrasonic devices');
				return;
			}
		} else if (entityType === 'custom') {
			deviceData.ip = document.getElementById('customIp').value.trim();
			deviceData.port = document.getElementById('customPort').value.trim();
			deviceData.connection_method = document.getElementById('customConnection').value;

			if (!deviceData.ip) {
				alert('IP Address is required for Custom devices');
				return;
			}
		}

		// Send request
		fetch('/api/devices', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(deviceData)
		})
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					bootstrap.Modal.getInstance(document.getElementById('addDeviceModal')).hide();
					location.reload(); // Refresh page to show new device
				} else {
					alert('Error adding device: ' + data.message);
				}
			})
			.catch(error => {
				console.error('Error adding device:', error);
				alert('Error adding device. Please try again.');
			});
	}

	function checkDeviceStatus(deviceId) {
		const btn = document.querySelector(`[data-device-id="${deviceId}"].check-status-btn`);
		const icon = btn.querySelector('i');
		icon.classList.add('spin');

		fetch(`/api/devices/${deviceId}/status`)
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					updateDeviceStatusInTable(deviceId, data.device_status);
				}
			})
			.catch(error => {
				console.error('Error checking device status:', error);
			})
			.finally(() => {
				icon.classList.remove('spin');
			});
	}

	function triggerDeviceReading(deviceId) {
		const btn = document.querySelector(`[data-device-id="${deviceId}"].trigger-reading-btn`);
		const icon = btn.querySelector('i');
		icon.classList.add('spin');

		fetch(`/api/devices/${deviceId}/trigger_reading`, {
			method: 'POST'
		})
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					showAlert('success', 'Reading triggered successfully');
				} else {
					showAlert('danger', 'Error triggering reading: ' + data.message);
				}
			})
			.catch(error => {
				console.error('Error triggering reading:', error);
				showAlert('danger', 'Error triggering reading');
			})
			.finally(() => {
				icon.classList.remove('spin');
			});
	}

	function showDeviceDetails(deviceId) {
		currentDeviceId = deviceId;
		const modal = new bootstrap.Modal(document.getElementById('deviceDetailsModal'));
		modal.show();

		const content = document.getElementById('deviceDetailsContent');
		content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

		// Load device details
		Promise.all([
			fetch(`/api/devices/${deviceId}`).then(r => r.json()),
			fetch(`/api/devices/${deviceId}/results?limit=10`).then(r => r.json()),
			fetch(`/api/devices/${deviceId}/commands?limit=5`).then(r => r.json())
		])
			.then(([deviceData, resultsData, commandsData]) => {
				if (deviceData.status === 'success') {
					content.innerHTML = generateDeviceDetailsHTML(
						deviceData.device,
						resultsData.results || [],
						commandsData.commands || []
					);
				} else {
					content.innerHTML = '<div class="alert alert-danger">Error loading device details</div>';
				}
			})
			.catch(error => {
				console.error('Error loading device details:', error);
				content.innerHTML = '<div class="alert alert-danger">Error loading device details</div>';
			});
	}

	function generateDeviceDetailsHTML(device, results, commands) {
		return `
        <div class="row">
            <div class="col-md-6">
                <h6>Device Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${device.name}</td></tr>
                    <tr><td><strong>Type:</strong></td><td>${device.entity_type}</td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${device.status === 'online' ? 'bg-success' : 'bg-danger'}">${device.status}</span></td></tr>
                    ${device.hioki_id ? `<tr><td><strong>Hioki ID:</strong></td><td>${device.hioki_id}</td></tr>` : ''}
                    ${device.ip ? `<tr><td><strong>IP Address:</strong></td><td>${device.ip}${device.port ? ':' + device.port : ''}</td></tr>` : ''}
                    <tr><td><strong>Connection:</strong></td><td>${device.connection_method || 'Unknown'}</td></tr>
                    <tr><td><strong>Last Update:</strong></td><td>${device.last_update || 'Never'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Recent Commands</h6>
                <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                    ${commands.length ? `
                        <table class="table table-sm">
                            <thead><tr><th>Time</th><th>Command</th></tr></thead>
                            <tbody>
                                ${commands.map(cmd => `
                                    <tr>
                                        <td><small>${new Date(cmd.timestamp).toLocaleString()}</small></td>
                                        <td><small>${cmd.tool_command || 'N/A'}</small></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-muted">No recent commands</p>'}
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>Recent Results</h6>
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    ${results.length ? `
                        <table class="table table-sm">
                            <thead><tr><th>Time</th><th>Type</th><th>Data</th></tr></thead>
                            <tbody>
                                ${results.map(result => `
                                    <tr>
                                        <td><small>${new Date(result.timestamp).toLocaleString()}</small></td>
                                        <td><small>${result.result_type || 'N/A'}</small></td>
                                        <td><small>${result.raw_data.substring(0, 100)}${result.raw_data.length > 100 ? '...' : ''}</small></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-muted">No recent results</p>'}
                </div>
            </div>
        </div>
    `;
	}

	function showRemoveDeviceModal(deviceId, deviceName) {
		currentDeviceId = deviceId;
		document.getElementById('removeDeviceName').textContent = deviceName;
		const modal = new bootstrap.Modal(document.getElementById('removeDeviceModal'));
		modal.show();
	}

	function confirmRemoveDevice() {
		if (!currentDeviceId) return;

		fetch(`/api/devices/${currentDeviceId}`, {
			method: 'DELETE'
		})
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					bootstrap.Modal.getInstance(document.getElementById('removeDeviceModal')).hide();
					location.reload(); // Refresh page
				} else {
					alert('Error removing device: ' + data.message);
				}
			})
			.catch(error => {
				console.error('Error removing device:', error);
				alert('Error removing device. Please try again.');
			});
	}

	// Utility functions
	function refreshAllDevices() {
		const btn = document.getElementById('refreshAllBtn');
		const icon = btn.querySelector('i');
		icon.classList.add('spin');

		// Trigger status check for all devices
		const deviceRows = document.querySelectorAll('[data-device-id]');
		const promises = Array.from(deviceRows).map(row => {
			const deviceId = row.getAttribute('data-device-id');
			return fetch(`/api/devices/${deviceId}/status`);
		});

		Promise.allSettled(promises)
			.then(() => {
				location.reload(); // Refresh page with updated data
			})
			.finally(() => {
				icon.classList.remove('spin');
			});
	}

	function refreshDeviceData() {
		// Update device data without full page reload
		fetch('/api/devices')
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					updateDevicesTable(data.devices);
					updateStatistics(data.devices);
				}
			})
			.catch(error => {
				console.error('Error refreshing device data:', error);
			});
	}

	function updateDevicesTable(devices) {
		devices.forEach(device => {
			const row = document.querySelector(`[data-device-id="${device.id}"]`);
			if (row) {
				// Update status badge
				const statusBadge = row.querySelector('.device-status-badge');
				if (statusBadge) {
					statusBadge.textContent = device.status || 'Unknown';
					statusBadge.className = `badge device-status-badge ${device.status === 'online' ? 'bg-success' :
						device.status === 'offline' ? 'bg-danger' : 'bg-secondary'
						}`;
				}

				// Update reading
				const readingCell = row.querySelector('.device-reading');
				if (readingCell) {
					readingCell.textContent = device.last_reading || 'N/A';
				}

				// Update timestamp
				const timestampCell = row.querySelector('.device-timestamp .last-update-time');
				if (timestampCell && device.last_update) {
					timestampCell.textContent = device.last_update;
					formatSingleTimestamp(timestampCell);
				}
			}
		});
	}

	function updateStatistics(devices) {
		const total = devices.length;
		const online = devices.filter(d => d.status === 'online').length;
		const offline = devices.filter(d => d.status === 'offline').length;

		document.getElementById('totalDevicesCount').textContent = total;
		document.getElementById('onlineDevicesCount').textContent = online;
		document.getElementById('offlineDevicesCount').textContent = offline;
	}

	function loadW610Status() {
		fetch('/api/w610/server_status')
			.then(response => response.json())
			.then(data => {
				if (data.status === 'success') {
					const connectedCount = Object.keys(data.devices || {}).length;
					document.getElementById('w610ConnectedCount').textContent = connectedCount;
				}
			})
			.catch(error => {
				console.error('Error loading W610 status:', error);
			});
	}

	function applyFilters() {
		const entityTypeFilter = document.getElementById('entityTypeFilter').value;
		const statusFilter = document.getElementById('statusFilter').value;
		const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

		const rows = document.querySelectorAll('#devicesTableBody tr');

		rows.forEach(row => {
			const entityType = row.getAttribute('data-entity-type');
			const status = row.getAttribute('data-status');
			const deviceName = row.querySelector('td strong').textContent.toLowerCase();
			const hiokiId = row.getAttribute('data-hioki-id').toLowerCase();

			let show = true;

			// Entity type filter
			if (entityTypeFilter && entityType !== entityTypeFilter) {
				show = false;
			}

			// Status filter
			if (statusFilter && status !== statusFilter) {
				show = false;
			}

			// Search filter
			if (searchFilter && !deviceName.includes(searchFilter) && !hiokiId.includes(searchFilter)) {
				show = false;
			}

			row.style.display = show ? '' : 'none';
		});
	}

	function updateDeviceStatusInTable(deviceId, newStatus) {
		const row = document.querySelector(`[data-device-id="${deviceId}"]`);
		if (row) {
			row.setAttribute('data-status', newStatus);
			const statusBadge = row.querySelector('.device-status-badge');
			if (statusBadge) {
				statusBadge.textContent = newStatus;
				statusBadge.className = `badge device-status-badge ${newStatus === 'online' ? 'bg-success' :
					newStatus === 'offline' ? 'bg-danger' : 'bg-secondary'
					}`;
			}
		}
	}

	function formatTimestamps() {
		document.querySelectorAll('.last-update-time').forEach(formatSingleTimestamp);
	}

	function formatSingleTimestamp(element) {
		const timestamp = element.textContent;
		if (timestamp && timestamp !== 'N/A' && timestamp !== 'Never') {
			try {
				const date = new Date(timestamp);
				if (!isNaN(date)) {
					element.textContent = date.toLocaleString();
				}
			} catch (e) {
				console.error('Error formatting timestamp:', e);
			}
		}
	}

	function showAlert(type, message) {
		const alertDiv = document.createElement('div');
		alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
		alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
		alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

		document.body.appendChild(alertDiv);

		// Auto-dismiss after 5 seconds
		setTimeout(() => {
			if (alertDiv.parentNode) {
				alertDiv.remove();
			}
		}, 5000);
	}

	// Socket.IO handlers for real-time updates
	const socket = io();

	socket.on('device_status_update', function (data) {
		updateDeviceStatusInTable(data.device_id, data.status);
	});

	socket.on('device_result_received', function (data) {
		if (data.hioki_id) {
			const row = document.querySelector(`[data-hioki-id="${data.hioki_id}"]`);
			if (row) {
				const readingCell = row.querySelector('.device-reading');
				if (readingCell) {
					if (data.processed_data && data.processed_data.impedance) {
						readingCell.textContent = data.processed_data.impedance + ' mΩ';
					} else {
						readingCell.textContent = data.raw_data.substring(0, 50);
					}
				}

				const timestampCell = row.querySelector('.device-timestamp .last-update-time');
				if (timestampCell) {
					timestampCell.textContent = new Date().toLocaleString();
				}
			}
		}
	});
</script>
{% endblock %}