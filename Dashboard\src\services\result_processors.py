import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from decimal import Decimal, InvalidOperation
import re
import os
from threading import Lock

# Optional MESDB support
try:
    import pyodbc

    MESDB_AVAILABLE = True
except ImportError:
    pyodbc = None
    MESDB_AVAILABLE = False

logger = logging.getLogger(__name__)

# MESDB Connection Configuration
MESDB_CONNECTION_STRING = os.environ.get(
    "MESDB_CONNECTION_STRING",
    "DRIVER={ODBC Driver 17 for SQL Server};SERVER=rds-bat-dev-use1-ignition-mssql.cs57qbiwugep.us-east-1.rds.amazonaws.com,1433;DATABASE=REVMES;UID=admin;PWD=rZ1Ph5QxZcmVrAtv;TrustServerCertificate=yes;",
)

# Database connection settings
MESDB_ENABLED = os.environ.get("MESDB_ENABLED", "true").lower() in (
    "true",
    "1",
    "yes",
    "on",
)
_connection_status = {"connected": False, "last_error": None, "last_check": None}

# Thread-safe connection management
_mesdb_lock = Lock()
_last_commands = {}  # device_id -> last command data


# =============================================================================
# MESDB Integration Functions
# =============================================================================


def get_mesdb_connection():
    """Get a connection to the MESDB database."""
    global _connection_status

    if not MESDB_AVAILABLE:
        error_msg = "MESDB not available - pyodbc not installed"
        logger.warning(error_msg)
        _connection_status.update(
            {"connected": False, "last_error": error_msg, "last_check": datetime.now()}
        )
        return None

    if not MESDB_ENABLED:
        error_msg = "MESDB disabled in configuration"
        logger.info(error_msg)
        _connection_status.update(
            {"connected": False, "last_error": error_msg, "last_check": datetime.now()}
        )
        return None

    try:
        conn = pyodbc.connect(MESDB_CONNECTION_STRING)
        _connection_status.update(
            {"connected": True, "last_error": None, "last_check": datetime.now()}
        )
        return conn
    except Exception as e:
        error_msg = f"Failed to connect to MESDB: {e}"
        logger.error(error_msg)
        _connection_status.update(
            {"connected": False, "last_error": error_msg, "last_check": datetime.now()}
        )
        return None


def test_mesdb_connection() -> Dict[str, Any]:
    """Test the MESDB connection and return status."""
    try:
        conn = get_mesdb_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM mes.VF_ToolData")
            count = cursor.fetchone()[0]
            conn.close()
            return {
                "connected": True,
                "error": None,
                "record_count": count,
                "server": "rds-bat-dev-use1-ignition-mssql.cs57qbiwugep.us-east-1.rds.amazonaws.com",
                "database": "REVMES",
                "table": "mes.VF_ToolData",
            }
        else:
            return {
                "connected": False,
                "error": _connection_status.get("last_error", "Unknown error"),
                "record_count": None,
                "server": "rds-bat-dev-use1-ignition-mssql.cs57qbiwugep.us-east-1.rds.amazonaws.com",
                "database": "REVMES",
                "table": "mes.VF_ToolData",
            }
    except Exception as e:
        return {
            "connected": False,
            "error": str(e),
            "record_count": None,
            "server": "rds-bat-dev-use1-ignition-mssql.cs57qbiwugep.us-east-1.rds.amazonaws.com",
            "database": "REVMES",
            "table": "mes.VF_ToolData",
        }


def get_mesdb_status() -> Dict[str, Any]:
    """Get current MESDB connection status."""
    return {
        "available": MESDB_AVAILABLE,
        "enabled": MESDB_ENABLED,
        "connection_status": _connection_status.copy(),
        "server": "rds-bat-dev-use1-ignition-mssql.cs57qbiwugep.us-east-1.rds.amazonaws.com",
        "database": "REVMES",
        "table": "mes.VF_ToolData",
    }


def set_mesdb_enabled(enabled: bool) -> bool:
    """Enable or disable MESDB connection."""
    global MESDB_ENABLED
    MESDB_ENABLED = enabled
    os.environ["MESDB_ENABLED"] = "true" if enabled else "false"
    logger.info(f"MESDB {'enabled' if enabled else 'disabled'}")
    return enabled


def store_command_in_mesdb(
    device_id: str, device_type: str, command_data: Dict[str, Any]
) -> Optional[str]:
    """Store a command in MESDB and return the command ID for linking."""
    try:
        with _mesdb_lock:
            conn = get_mesdb_connection()
            if not conn:
                return None

            cursor = conn.cursor()

            # Extract command context
            context = command_data.get("context", {})
            command_id = (
                command_data.get("commandId")
                or f"{device_id}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            )

            # Store the command
            cursor.execute(
                """
                INSERT INTO mes.VF_ToolData (
                    DeviceID, DeviceType, RecordType, CommandID, ToolCommand, ToolCommandData,
                    WorkStationName, PersonName, TestItemName, ActivityID, TaskName, RouteName,
                    OptionCode, LotID, UnitID, WorkOrderNumber, RawData
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    device_id,
                    device_type,
                    "COMMAND",
                    command_id,
                    command_data.get("toolCommand"),
                    json.dumps(command_data),
                    context.get("WorkStationName"),
                    context.get("PersonName"),
                    context.get("TestItemName"),
                    context.get("ActivityID"),
                    context.get("TaskName"),
                    context.get("RouteName"),
                    context.get("OptionCode"),
                    context.get("LotID"),
                    context.get("UnitID"),
                    context.get("WorkOrderNumber"),
                    json.dumps(command_data),
                ),
            )

            conn.commit()
            conn.close()

            # Store as last command for this device
            _last_commands[device_id] = {
                "command_id": command_id,
                "context": context,
                "timestamp": datetime.now(),
            }

            logger.info(f"Stored command {command_id} for device {device_id} in MESDB")
            return command_id

    except Exception as e:
        logger.error(f"Failed to store command in MESDB: {e}")
        return None


def store_result_in_mesdb(
    device_id: str, device_type: str, result_data: Dict[str, Any], raw_data: str
) -> bool:
    """Store a result in MESDB and link it to the most recent command."""
    try:
        with _mesdb_lock:
            conn = get_mesdb_connection()
            if not conn:
                return False

            cursor = conn.cursor()

            # Get the last command for this device
            last_command = _last_commands.get(device_id)
            linked_command_id = last_command.get("command_id") if last_command else None
            command_context = last_command.get("context", {}) if last_command else {}

            # Extract result information
            result_value = None
            result_unit = None
            result_status = "UNKNOWN"

            if device_type == "hioki":
                result_value = result_data.get("impedance")
                result_unit = result_data.get("unit", "milliohms")
                result_status = (
                    "PASS"
                    if result_data.get("impedance") and "error" not in result_data
                    else "ERROR"
                )
            elif device_type == "cts":
                result_value = f"PL:{result_data.get('pressureloss', 'N/A')}, TP:{result_data.get('testpressure', 'N/A')}"
                result_unit = "pressure"
                result_status = (
                    "PASS"
                    if result_data.get("pressureloss")
                    or result_data.get("testpressure")
                    else "ERROR"
                )
            elif device_type == "ultrasonic":
                result_value = str(result_data.get("weld_result", False))
                result_unit = "boolean"
                result_status = "PASS" if result_data.get("weld_result") else "FAIL"

            # Store the result
            cursor.execute(
                """
                INSERT INTO mes.VF_ToolData (
                    DeviceID, DeviceType, RecordType, LinkedCommandID, ResultData, ResultValue,
                    ResultUnit, ResultStatus, RawData, WorkStationName, PersonName, TestItemName,
                    ActivityID, TaskName, RouteName, OptionCode, LotID, UnitID, WorkOrderNumber
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    device_id,
                    device_type,
                    "RESULT",
                    linked_command_id,
                    json.dumps(result_data),
                    result_value,
                    result_unit,
                    result_status,
                    raw_data,
                    command_context.get("WorkStationName"),
                    command_context.get("PersonName"),
                    command_context.get("TestItemName"),
                    command_context.get("ActivityID"),
                    command_context.get("TaskName"),
                    command_context.get("RouteName"),
                    command_context.get("OptionCode"),
                    command_context.get("LotID"),
                    command_context.get("UnitID"),
                    command_context.get("WorkOrderNumber"),
                ),
            )

            conn.commit()
            conn.close()

            logger.info(
                f"Stored result for device {device_id} linked to command {linked_command_id} in MESDB"
            )
            return True

    except Exception as e:
        logger.error(f"Failed to store result in MESDB: {e}")
        return False


# =============================================================================
# Legacy Data Formatter Functions (migrated from data_formatter.py)
# =============================================================================


def format_data(payload: str) -> Optional[str]:
    """Format Hioki reading and remove scientific notation.

    DEPRECATED: Use HiokiResultProcessor.process() instead.
    This function is kept for backward compatibility.

    The W610 returns readings in scientific notation (e.g. ``00.81394E-03``).
    This helper converts the value to milliohms and returns a decimal string
    without an exponent so it can be safely published to MQTT and stored in
    the database.
    """
    try:
        value = Decimal(payload.strip()) * Decimal("1000")
        # Quantize to 4 decimal places and convert to plain string
        value = value.quantize(Decimal("0.0001"))
        return format(value, "f")
    except (InvalidOperation, ValueError) as e:
        logger.error(f"Error formatting data: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in data formatting: {str(e)}")
        return None


def create_mqtt_topic(hioki_number: str) -> str:
    """Create MQTT topic for Hioki device.

    DEPRECATED: Use HiokiResultProcessor.get_mqtt_topic() instead.
    This function is kept for backward compatibility.
    """
    return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_number}/result/impedance"


def validate_hioki_number(hioki_number: str) -> bool:
    """Validate Hioki device number format."""
    if not hioki_number:
        return False
    return (
        len(hioki_number) == 4 and hioki_number[0] == "H" and hioki_number[1:].isdigit()
    )


def is_valid_reading(reading) -> bool:
    """Check if reading is within valid range."""
    try:
        value = float(reading)
        return -9999.0 < value < 9999.0
    except Exception:
        return False


def format_reading_for_display(value: Union[float, str]) -> str:
    """Format reading for display with units."""
    try:
        if isinstance(value, str):
            value = float(value)
        return f"{value:.4f} mΩ"
    except (ValueError, TypeError):
        return "N/A"


class BaseResultProcessor:
    """Base class for device result processors."""

    def __init__(self, entity_type: str):
        self.entity_type = entity_type

    def process(self, raw_data: str, device_config: Dict = None) -> Dict[str, Any]:
        """Process raw data into standardized result format."""
        raise NotImplementedError

    def get_mqtt_topic(self, device_id: str, device_config: Dict = None) -> str:
        """Get MQTT topic for publishing results."""
        raise NotImplementedError


class HiokiResultProcessor(BaseResultProcessor):
    """Process Hioki impedance readings."""

    def __init__(self):
        super().__init__("hioki")

    def process(self, raw_data: str, device_config: Dict = None) -> Dict[str, Any]:
        """Process Hioki reading and convert to milliohms."""
        try:
            # Convert from scientific notation to milliohms
            value = Decimal(raw_data.strip()) * Decimal("1000")
            formatted_value = format(value.quantize(Decimal("0.0001")), "f")

            return {
                "impedance": formatted_value,
                "unit": "milliohms",
                "raw_value": raw_data.strip(),
                "timestamp": datetime.now().isoformat(),
                "device_type": "hioki",
            }
        except (InvalidOperation, ValueError) as e:
            logger.error(f"Error processing Hioki data '{raw_data}': {e}")
            return {
                "error": f"Invalid reading: {str(e)}",
                "raw_value": raw_data,
                "timestamp": datetime.now().isoformat(),
                "device_type": "hioki",
            }

    def get_mqtt_topic(self, device_id: str, device_config: Dict = None) -> str:
        """Get MQTT topic for Hioki devices."""
        return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{device_id}/result/impedance"


class CTSResultProcessor(BaseResultProcessor):
    """Process CTS tester readings using NodeRED-style logic."""

    def __init__(self):
        super().__init__("cts")

    def process(self, raw_data: str, device_config: Dict = None) -> Dict[str, Any]:
        """Process CTS data similar to NodeRED logic."""
        if not raw_data or "*" in raw_data or "x01" in raw_data:
            return None

        parts = raw_data.split()
        if not parts:
            return None

        def search_string_in_array(pattern: str, str_array: List[str]) -> int:
            """Find index of exact pattern match."""
            for j, val in enumerate(str_array):
                if re.fullmatch(pattern, val):
                    return j
            return -1

        def contain_string_in_array(substr: str, str_array: List[str]) -> int:
            """Find index of substring match."""
            for j, val in enumerate(str_array):
                if substr in val:
                    return j
            return -1

        # Find indices for key markers (similar to NodeRED searchStringInArray)
        msg1_ind = search_string_in_array(r"dpsig", parts) - 1
        msg2_ind = search_string_in_array(r"psig", parts) - 1
        msg3_ind = contain_string_in_array("|GVB|", parts)

        # Extract primary values
        pressureloss = (
            parts[msg1_ind] if msg1_ind >= 0 and msg1_ind < len(parts) else None
        )
        testpressure = (
            parts[msg2_ind] if msg2_ind >= 0 and msg2_ind < len(parts) else None
        )
        serial_info = (
            parts[msg3_ind] if msg3_ind != -1 and msg3_ind < len(parts) else None
        )

        if pressureloss is None and testpressure is None:
            return None

        # Current time for logging
        current_time = datetime.now().strftime("%I:%M:%S %p")

        # Filter array and extract specific indices (similar to second NodeRED node)
        filtered_parts = [
            item for item in parts if item and item.strip() and item != ""
        ]

        # Extract data at specific indices: [8, 6, 2, 4, 5, 10, 12, 14, 15, 17, 18]
        standardized_data = []
        indices_to_extract = [8, 6, 2, 4, 5, 10, 12, 14, 15, 17, 18]

        for idx in indices_to_extract:
            if idx < len(filtered_parts):
                standardized_data.append(filtered_parts[idx])
            else:
                standardized_data.append("")

        # Create formatted serial message (msg3 equivalent)
        device_name = device_config.get("name", "CTS") if device_config else "CTS"
        serial_message = (
            f"{device_name} SN: {serial_info} | Time: {current_time}"
            if serial_info
            else None
        )

        return {
            "pressureloss": pressureloss,
            "testpressure": testpressure,
            "serial_info": serial_info,
            "serial_message": serial_message,
            "standardized_array": standardized_data,
            "standardized_string": ",".join(standardized_data),
            "raw_line": raw_data,
            "timestamp": datetime.now().isoformat(),
            "device_type": "cts",
            "test_time": current_time,
        }

    def get_mqtt_topic(self, device_id: str, device_config: Dict = None) -> str:
        """Get MQTT topic for CTS devices."""
        cts_type = (
            device_config.get("cts_type", "manifold") if device_config else "manifold"
        )

        if cts_type.lower() == "manifold":
            base_topic = "nomuda/gvl/tools/GVB-CTS-Manifold"
        else:
            base_topic = "nomuda/gvl/tools/GVB-CTS-Enclosure"

        return f"{base_topic}/{device_id}/result"

    def get_mqtt_topics(
        self, device_id: str, device_config: Dict = None
    ) -> Dict[str, str]:
        """Get all MQTT topics for CTS (multiple topics for different values)."""
        base_topic = self.get_mqtt_topic(device_id, device_config)

        return {
            "pressureloss": f"{base_topic}/pressureloss",
            "testpressure": f"{base_topic}/testpressure",
            "test": f"{base_topic}/test",  # For raw line
        }


class WelderResultProcessor(BaseResultProcessor):
    """Process ultrasonic welder results from individual MQTT topics."""

    def __init__(self):
        super().__init__("welder")
        # Store partial data for aggregation
        self._partial_data = {}

    def process(self, raw_data: str, device_config: Dict = None) -> Dict[str, Any]:
        """Process individual welder data field or complete JSON data."""
        try:
            # Check if this is JSON data (legacy format)
            if raw_data.strip().startswith("{"):
                return self._process_json_data(raw_data)

            # This is an individual field value from MQTT topic
            # We need the topic info to know which field this is
            # For now, return a basic result structure
            return {
                "field_value": raw_data.strip(),
                "timestamp": datetime.now().isoformat(),
                "device_type": "welder",
                "raw_data": raw_data,
                "processor_type": "ultrasonic",
            }

        except Exception as e:
            logger.error(f"Error processing welder data '{raw_data}': {e}")
            return {
                "error": f"Processing error: {str(e)}",
                "raw_data": raw_data,
                "timestamp": datetime.now().isoformat(),
                "device_type": "welder",
            }

    def _process_json_data(self, raw_data: str) -> Dict[str, Any]:
        """Process complete JSON welder data (legacy format)."""
        try:
            if isinstance(raw_data, str):
                welder_data = json.loads(raw_data)
            else:
                welder_data = raw_data

            # Extract the 'last' data which contains the weld results
            last_weld = welder_data.get("last", {})

            # Standardize the key measurements
            standardized = {
                "weld_result": welder_data.get("ok", False),
                "energy": self._safe_float(last_weld.get("energy")),
                "power": self._safe_float(last_weld.get("weld_power")),
                "amplitude": self._safe_float(last_weld.get("amplitude")),
                "weld_time": self._safe_float(last_weld.get("weld_time")),
                "pressure": self._safe_float(last_weld.get("pressure")),
                "height": self._safe_float(last_weld.get("height")),
                "pre_height": self._safe_float(last_weld.get("pre_height")),
                "cycle_count": self._safe_int(last_weld.get("cycle_count")),
                "part_name": last_weld.get("part_name"),
                "date": last_weld.get("date"),
                "time": last_weld.get("time"),
                "timestamp": last_weld.get("timestamp", datetime.now().isoformat()),
                "device_type": "welder",
                # Quality parameters
                "height_minus": self._safe_float(last_weld.get("height_minus")),
                "height_plus": self._safe_float(last_weld.get("height_plus")),
                "power_minus": self._safe_float(last_weld.get("power_minus")),
                "power_plus": self._safe_float(last_weld.get("power_plus")),
                "time_minus": self._safe_float(last_weld.get("time_minus")),
                "time_plus": self._safe_float(last_weld.get("time_plus")),
                "preheight_minus": self._safe_float(last_weld.get("preheight_minus")),
                "preheight_plus": self._safe_float(last_weld.get("preheight_plus")),
                # Additional data
                "trigger_pressure": self._safe_float(last_weld.get("trigger_pressure")),
                "alarms": last_weld.get("alarms"),
                "full_data": last_weld,  # Keep original data
                "raw_json": (
                    raw_data if isinstance(raw_data, str) else json.dumps(raw_data)
                ),
            }

            return standardized

        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error processing welder JSON data '{raw_data}': {e}")
            return {
                "error": f"Invalid JSON: {str(e)}",
                "raw_data": raw_data,
                "timestamp": datetime.now().isoformat(),
                "device_type": "welder",
            }

    def _safe_float(self, value) -> Optional[float]:
        """Safely convert value to float."""
        if value is None or value == "" or value == "-":
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    def _safe_int(self, value) -> Optional[int]:
        """Safely convert value to int."""
        if value is None or value == "" or value == "-":
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def get_mqtt_topics(
        self, device_id: str, device_config: Dict = None
    ) -> Dict[str, str]:
        """Get MQTT topics for ultrasonic welder results."""
        # Use welder name if available, otherwise device_id
        welder_name = device_config.get("welder_name") if device_config else None
        topic_name = welder_name or device_id
        base_topic = f"nomuda/gvl/tools/GVB-BRANSON/{topic_name}/result"

        return {
            "weld_data": f"{base_topic}/weld_data",
            "energy": f"{base_topic}/energy",
            "weld_results": f"{base_topic}/weld_results",
            "weld_time": f"{base_topic}/weld_time",
            "weld_power": f"{base_topic}/weld_power",
            "pressure": f"{base_topic}/pressure",
            "amplitude": f"{base_topic}/amplitude",
            "height": f"{base_topic}/height",
            "part_name": f"{base_topic}/part_name",
            "cycle_count": f"{base_topic}/cycle_count",
            "alarms": f"{base_topic}/alarms",
        }

    def get_mqtt_topic(self, device_id: str, device_config: Dict = None) -> str:
        """Get MQTT topic for welder devices."""
        # Use welder name if available, otherwise raspi name
        welder_name = device_config.get("welder_name") if device_config else None
        topic_name = welder_name or device_id

        return f"nomuda/gvl/tools/GVB-BRANSON/{topic_name}/result/weld_data"


class CustomResultProcessor(BaseResultProcessor):
    """Process custom device results."""

    def __init__(self):
        super().__init__("custom")

    def process(self, raw_data: str, device_config: Dict = None) -> Dict[str, Any]:
        """Process custom device data based on configuration."""
        in_protocol = (
            device_config.get("in_protocol", "unknown") if device_config else "unknown"
        )

        # Try to parse as JSON first
        try:
            if raw_data.strip().startswith("{"):
                json_data = json.loads(raw_data)
                return {
                    "data": json_data,
                    "protocol": in_protocol,
                    "timestamp": datetime.now().isoformat(),
                    "device_type": "custom",
                    "raw_data": raw_data,
                }
        except json.JSONDecodeError:
            pass

        # Treat as plain text
        return {
            "data": raw_data.strip(),
            "protocol": in_protocol,
            "timestamp": datetime.now().isoformat(),
            "device_type": "custom",
            "raw_data": raw_data,
        }

    def get_mqtt_topic(self, device_id: str, device_config: Dict = None) -> str:
        """Get MQTT topic for custom devices."""
        return f"nomuda/gvl/tools/Custom/{device_id}/result/data"


class ResultProcessorFactory:
    """Factory for creating result processors."""

    _processors = {
        "hioki_bridge": HiokiResultProcessor(),
        "hioki_w610": HiokiResultProcessor(),
        "hioki_serial": HiokiResultProcessor(),
        "hioki_http": HiokiResultProcessor(),
        "cts": CTSResultProcessor(),
        "welder": WelderResultProcessor(),
        "ultrasonic": WelderResultProcessor(),  # Ultrasonic welders use welder processor
        "custom": CustomResultProcessor(),
    }

    @classmethod
    def get_processor(cls, entity_type: str) -> BaseResultProcessor:
        """Get processor for entity type."""
        # Handle generic 'hioki' type
        if entity_type == "hioki":
            entity_type = "hioki_w610"  # Default to W610

        processor = cls._processors.get(entity_type)
        if not processor:
            logger.warning(f"No processor found for entity type: {entity_type}")
            return cls._processors["custom"]  # Fallback to custom processor

        return processor

    @classmethod
    def process_result(
        cls, entity_type: str, device_id: str, raw_data: str, device_config: Dict = None
    ) -> Optional[Dict[str, Any]]:
        """Process a result using the appropriate processor."""
        processor = cls.get_processor(entity_type)

        try:
            result = processor.process(raw_data, device_config)
            if result:
                # Add common metadata
                result["processor_type"] = entity_type
                result["device_id"] = device_id
                result["processed_at"] = datetime.now().isoformat()

                # Store result in MESDB
                try:
                    store_result_in_mesdb(device_id, entity_type, result, raw_data)
                except Exception as mesdb_error:
                    logger.error(f"Failed to store result in MESDB: {mesdb_error}")
                    # Don't fail the entire process if MESDB storage fails

            return result
        except Exception as e:
            logger.error(
                f"Error processing result for {entity_type} device {device_id}: {e}"
            )
            return {
                "error": str(e),
                "processor_type": entity_type,
                "device_id": device_id,
                "raw_data": raw_data,
                "processed_at": datetime.now().isoformat(),
            }

    @classmethod
    def store_command(
        cls, entity_type: str, device_id: str, command_data: Dict[str, Any]
    ) -> Optional[str]:
        """Store a command and return command ID for linking to results."""
        try:
            return store_command_in_mesdb(device_id, entity_type, command_data)
        except Exception as e:
            logger.error(
                f"Failed to store command for {entity_type} device {device_id}: {e}"
            )
            return None

    @classmethod
    def get_mqtt_topic(
        cls, entity_type: str, device_id: str, device_config: Dict = None
    ) -> str:
        """Get MQTT topic for publishing."""
        processor = cls.get_processor(entity_type)
        return processor.get_mqtt_topic(device_id, device_config)
