from src.app import create_app, get_local_ip, socketio


def main():
    """Main entry point for production."""
    app = create_app()

    print("Starting HexMES Wireless Admin in production mode...")
    local_ip = get_local_ip()
    print(f"Access the dashboard at: http://{local_ip}:8000")
    print(f"Also available at: http://localhost:8000")

    # Run the application with SocketIO
    try:
        socketio.run(app, host="0.0.0.0", port=8000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error starting application: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
