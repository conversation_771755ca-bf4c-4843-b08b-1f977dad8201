#!/usr/bin/env python3
"""
VF Command Verification Script
Verifies that all your existing tools are being monitored for VF commands.
Run this to ensure nothing is missed before migration.
"""

import os
import sys
import sqlite3
import json
from typing import Dict, List, Set

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


def get_all_existing_devices(db_path: str) -> List[Dict]:
    """Get all devices from existing tables."""
    devices = []

    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get bridge devices
        try:
            cursor.execute(
                """
                SELECT d.hioki_id, d.tester_type, b.name as bridge_name, 
                       'hioki_bridge' as device_category, b.ip, d.port
                FROM devices d
                JOIN bridges b ON d.bridge_id = b.id
            """
            )
            for row in cursor.fetchall():
                devices.append(
                    {
                        "device_id": row["hioki_id"],
                        "device_type": "Hioki-RM3545",  # Standard Hioki type
                        "category": row["device_category"],
                        "connection": f"Bridge: {row['bridge_name']} ({row['ip']}:{row['port']})",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-Hioki-RM3545-{row['hioki_id']}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

        # Get serial devices
        try:
            cursor.execute(
                """
                SELECT hioki_id, ip, port, tester_type
                FROM devices 
                WHERE device_type = 'serial' AND bridge_id IS NULL
            """
            )
            for row in cursor.fetchall():
                devices.append(
                    {
                        "device_id": row["hioki_id"],
                        "device_type": "Hioki-RM3545",
                        "category": "hioki_serial",
                        "connection": f"Serial: {row['ip']}:{row['port']}",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-Hioki-RM3545-{row['hioki_id']}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

        # Get W610 devices
        try:
            cursor.execute(
                """
                SELECT hioki_id, ip, port
                FROM devices 
                WHERE device_type = 'w610' AND bridge_id IS NULL
            """
            )
            for row in cursor.fetchall():
                devices.append(
                    {
                        "device_id": row["hioki_id"],
                        "device_type": "Hioki-RM3545",
                        "category": "hioki_w610",
                        "connection": f"W610: {row['ip']}:{row['port']}",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-Hioki-RM3545-{row['hioki_id']}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

        # Get CTS devices
        try:
            cursor.execute("SELECT name, cts_id, cts_type, ip, port FROM cts")
            for row in cursor.fetchall():
                cts_id = row["cts_id"] or row["name"]
                cts_type = row["cts_type"] or "Manifold"

                devices.append(
                    {
                        "device_id": cts_id,
                        "device_type": f"CTS-{cts_type}",
                        "category": "cts",
                        "connection": f"CTS: {row['ip']}:{row['port']}",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-CTS-{cts_type}-{cts_id}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

        # Get welders
        try:
            cursor.execute("SELECT raspi_name, welder_name, ip, port FROM welders")
            for row in cursor.fetchall():
                welder_name = row["welder_name"] or row["raspi_name"]

                devices.append(
                    {
                        "device_id": welder_name,
                        "device_type": "Welder",
                        "category": "welder",
                        "connection": f"Welder: {row['ip']}:{row['port']}",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-BRANSON-{welder_name}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

        # Get custom devices
        try:
            cursor.execute("SELECT name, ip, port, in_protocol FROM custom_devices")
            for row in cursor.fetchall():
                devices.append(
                    {
                        "device_id": row["name"],
                        "device_type": "Custom",
                        "category": "custom",
                        "connection": f"Custom: {row['ip']}:{row['port']} ({row['in_protocol']})",
                        "expected_command_topic": f"nomuda/gvl/tools/GVB-Custom-{row['name']}/command",
                    }
                )
        except sqlite3.OperationalError:
            pass

    return devices


def get_captured_vf_commands(db_path: str) -> List[Dict]:
    """Get VF commands that have been captured."""
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT device_type, device_id, COUNT(*) as command_count,
                       MAX(timestamp) as last_command,
                       MAX(work_order_number) as last_work_order,
                       MAX(unit_id) as last_unit_id
                FROM vf_commands 
                GROUP BY device_type, device_id
                ORDER BY last_command DESC
            """
            )

            return [dict(row) for row in cursor.fetchall()]
    except sqlite3.OperationalError:
        # VF commands table doesn't exist yet
        return []


def generate_mqtt_subscription_list(devices: List[Dict]) -> List[str]:
    """Generate list of MQTT topics that should be subscribed to."""
    topics = set()

    for device in devices:
        topics.add(device["expected_command_topic"])

    # Also add wildcard patterns
    topics.add("nomuda/gvl/tools/+/command")
    topics.add("nomuda/gvl/tools/GVB-+/command")
    topics.add("nomuda/gvl/tools/GVB-Hioki-+/command")
    topics.add("nomuda/gvl/tools/GVB-CTS-+/command")
    topics.add("nomuda/gvl/tools/GVB-BRANSON-+/command")
    topics.add("nomuda/gvl/tools/GVB-Custom-+/command")

    return sorted(list(topics))


def check_vf_command_coverage(
    devices: List[Dict], captured_commands: List[Dict]
) -> Dict:
    """Check which devices have VF command coverage."""

    # Create lookup of captured commands
    captured_lookup = {}
    for cmd in captured_commands:
        key = f"{cmd['device_type']}-{cmd['device_id']}"
        captured_lookup[key] = cmd

    covered_devices = []
    uncovered_devices = []

    for device in devices:
        # Try different key formats to match captured commands
        possible_keys = [
            f"{device['device_type']}-{device['device_id']}",
            f"{device['device_type'].split('-')[0]}-{device['device_id']}",  # Hioki-RM3545 -> Hioki
            f"{device['device_id']}",  # Just device ID
        ]

        found = False
        for key in possible_keys:
            if key in captured_lookup:
                covered_devices.append({**device, "vf_commands": captured_lookup[key]})
                found = True
                break

        if not found:
            uncovered_devices.append(device)

    return {
        "total_devices": len(devices),
        "covered_devices": covered_devices,
        "uncovered_devices": uncovered_devices,
        "coverage_percentage": (
            (len(covered_devices) / len(devices) * 100) if devices else 0
        ),
    }


def print_verification_report(db_path: str):
    """Print comprehensive verification report."""

    print("VF COMMAND VERIFICATION REPORT")
    print("=" * 60)

    # Get all existing devices
    devices = get_all_existing_devices(db_path)
    print(f"Found {len(devices)} devices in existing system:")

    device_counts = {}
    for device in devices:
        category = device["category"]
        device_counts[category] = device_counts.get(category, 0) + 1

    for category, count in device_counts.items():
        print(f"  {category}: {count} devices")

    print()

    # Get captured VF commands
    captured_commands = get_captured_vf_commands(db_path)

    if not captured_commands:
        print("⚠️  NO VF COMMANDS CAPTURED YET")
        print("   You need to start VF command monitoring before migration!")
        print()
        print("Next steps:")
        print("1. Add VF command capture to your app.py")
        print("2. Restart your HexMES application")
        print("3. Trigger some tests from Visual Factory")
        print("4. Re-run this verification script")
        print()
        print_expected_topics(devices)
        return

    print(f"Found {len(captured_commands)} devices with captured VF commands:")
    for cmd in captured_commands:
        print(
            f"  {cmd['device_type']}-{cmd['device_id']}: {cmd['command_count']} commands (last: {cmd['last_command']})"
        )

    print()

    # Check coverage
    coverage = check_vf_command_coverage(devices, captured_commands)

    print("VF COMMAND COVERAGE ANALYSIS")
    print("-" * 40)
    print(
        f"Coverage: {len(coverage['covered_devices'])}/{coverage['total_devices']} devices ({coverage['coverage_percentage']:.1f}%)"
    )
    print()

    if coverage["covered_devices"]:
        print("✅ DEVICES WITH VF COMMAND COVERAGE:")
        for device in coverage["covered_devices"]:
            vf_cmd = device["vf_commands"]
            print(f"  {device['device_id']} ({device['category']})")
            print(f"    Commands: {vf_cmd['command_count']}")
            print(f"    Last Work Order: {vf_cmd['last_work_order']}")
            print(f"    Last Unit ID: {vf_cmd['last_unit_id']}")
            print()

    if coverage["uncovered_devices"]:
        print("❌ DEVICES WITHOUT VF COMMAND COVERAGE:")
        for device in coverage["uncovered_devices"]:
            print(f"  {device['device_id']} ({device['category']})")
            print(f"    Connection: {device['connection']}")
            print(f"    Expected topic: {device['expected_command_topic']}")
            print()

        print(
            "⚠️  These devices may not receive VF commands, or the topic format may be different."
        )
        print("   Verify with Visual Factory configuration.")

    print()

    # Migration readiness
    if coverage["coverage_percentage"] >= 80:
        print("✅ MIGRATION READY")
        print("   Most devices have VF command coverage.")
        print("   You can proceed with migration to unified system.")
    else:
        print("⚠️  MIGRATION NOT READY")
        print("   Some devices lack VF command coverage.")
        print("   Investigate missing devices before migration.")

    print()
    print_expected_topics(devices)


def print_expected_topics(devices: List[Dict]):
    """Print expected MQTT topics for monitoring."""
    topics = generate_mqtt_subscription_list(devices)

    print("EXPECTED MQTT COMMAND TOPICS")
    print("-" * 30)
    print("Your VF command monitor should subscribe to these topics:")
    print()

    # Group by pattern
    specific_topics = [t for t in topics if "+" not in t]
    wildcard_topics = [t for t in topics if "+" in t]

    if wildcard_topics:
        print("Wildcard patterns (recommended):")
        for topic in wildcard_topics:
            print(f"  {topic}")
        print()

    if len(specific_topics) <= 20:
        print("Specific device topics:")
        for topic in specific_topics:
            print(f"  {topic}")
    else:
        print(f"Specific device topics: {len(specific_topics)} total")
        print("(Use wildcard patterns above to cover all)")


def main():
    """Main verification function."""
    # Get database path
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DB_PATH = os.path.join(BASE_DIR, "dashboard.db")

    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found: {DB_PATH}")
        print("Please ensure your HexMES app has been run at least once.")
        return

    print_verification_report(DB_PATH)


if __name__ == "__main__":
    main()
