import sqlite3
import os
import logging
import json
from datetime import datetime

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_PATH = os.path.join(BASE_DIR, "dashboard.db")

logger = logging.getLogger(__name__)


def init_db():
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS bridges (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            ip TEXT NOT NULL,
            port INTEGER DEFAULT 5000,
            status TEXT DEFAULT 'unknown',
            last_seen TIMESTAMP,
            conn_method TEXT DEFAULT 'bridge',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS devices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bridge_id INTEGER,
            hioki_id TEXT NOT NULL,
            port TEXT,
            ip TEXT,
            status TEXT DEFAULT 'unknown',
            last_reading TEXT,
            last_update TIMESTAMP,
            testing INTEGER DEFAULT 0,
            device_type TEXT,
            tester_type TEXT DEFAULT 'standard',
            FOREIGN KEY (bridge_id) REFERENCES bridges (id)
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS cts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            ip TEXT NOT NULL,
            port TEXT NOT NULL,
            cts_type TEXT NOT NULL DEFAULT 'manifold',
            cts_id TEXT,
            status TEXT DEFAULT 'unknown',
            testpressure TEXT,
            pressureloss TEXT,
            last_reading TEXT,
            last_update TIMESTAMP
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS custom_devices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            ip TEXT NOT NULL,
            port TEXT NOT NULL,
            in_protocol TEXT NOT NULL,
            in_param TEXT,
            out_protocol TEXT NOT NULL,
            out_param TEXT
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS welders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            raspi_name TEXT NOT NULL,
            ip TEXT NOT NULL,
            port INTEGER DEFAULT 8080,
            welder_name TEXT,
            status TEXT DEFAULT 'unknown',
            last_reading TEXT,
            last_update TIMESTAMP
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS api_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token TEXT NOT NULL UNIQUE,
            username TEXT,
            expires_at TIMESTAMP
        )
        """
        )
        cursor.execute(
            """
        CREATE TABLE IF NOT EXISTS device_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id INTEGER NOT NULL,
            device_type TEXT NOT NULL,
            data TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )
        conn.commit()

        # Add tester_type column to existing devices table if missing
        cursor.execute("PRAGMA table_info(devices)")
        columns = [row[1] for row in cursor.fetchall()]
        if "tester_type" not in columns:
            cursor.execute(
                "ALTER TABLE devices ADD COLUMN tester_type TEXT DEFAULT 'standard'"
            )
            conn.commit()

        # Add cts_type column to existing cts table if missing
        cursor.execute("PRAGMA table_info(cts)")
        cts_columns = [row[1] for row in cursor.fetchall()]
        if "cts_type" not in cts_columns:
            cursor.execute(
                "ALTER TABLE cts ADD COLUMN cts_type TEXT DEFAULT 'manifold'"
            )
            conn.commit()


def get_db_connection():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn


def create_api_token(username, expire_minutes=None):
    import secrets
    from datetime import timezone, timedelta

    token = secrets.token_hex(32)
    expires_at = None
    if expire_minutes is not None:
        expires_at = (
            datetime.now(timezone.utc) + timedelta(minutes=expire_minutes)
        ).isoformat()
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO api_tokens (token, username, expires_at) VALUES (?, ?, ?)",
            (token, username, expires_at),
        )
        conn.commit()
    return token


def validate_api_token(token):
    if not token:
        return None
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT username, expires_at FROM api_tokens WHERE token = ?",
            (token,),
        )
        row = cursor.fetchone()
        if not row:
            return None
        username, expires_at = row
        if expires_at is None:
            return username
        try:
            if datetime.fromisoformat(expires_at) >= datetime.now(
                datetime.timezone.utc
            ):
                return username
        except Exception:
            pass
    return None


# Bridge and CTS utilities


def get_bridges():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM bridges ORDER BY name")
        return [dict(row) for row in cursor.fetchall()]


def get_bridge(bridge_id):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM bridges WHERE id = ?", (bridge_id,))
        result = cursor.fetchone()
        return dict(result) if result else None


def get_cts():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM cts ORDER BY id")
        return [dict(row) for row in cursor.fetchall()]


def get_cts_by_id(id):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM cts WHERE id = ?", (id,))
        result = cursor.fetchone()
        return dict(result) if result else None


def add_cts(name, ip, port=23, cts_id=None, cts_type="manifold"):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO cts (name, ip, port, cts_id, cts_type) VALUES (?, ?, ?, ?, ?)",
            (name, ip, port, cts_id, cts_type),
        )
        conn.commit()
        return cursor.lastrowid


def add_bridge(name, ip, port=5000):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO bridges (name, ip, port) VALUES (?, ?, ?)",
            (name, ip, port),
        )
        conn.commit()
        return cursor.lastrowid


def update_bridge_status(bridge_id, status):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE bridges SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?",
            (status, bridge_id),
        )
        conn.commit()


def update_cts_status(cts_id, status):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE cts SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?",
            (status, cts_id),
        )
        conn.commit()


def update_cts_reading(cts_id, data):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE cts
            SET testpressure = ?, pressureloss = ?, last_reading = ?, last_update = CURRENT_TIMESTAMP, status = 'online'
            WHERE id = ?
            """,
            (
                data.get("testpressure"),
                data.get("pressureloss"),
                data.get("raw"),
                cts_id,
            ),
        )
        cursor.execute(
            "INSERT INTO device_history (device_id, device_type, data) VALUES (?, ?, ?)",
            (cts_id, "cts", json.dumps(data)),
        )
        conn.commit()


def update_device_status_by_hioki_id(hioki_id, status):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE devices SET status = ?, last_update = CURRENT_TIMESTAMP WHERE hioki_id = ? AND device_type = 'w610'",
            (status, hioki_id),
        )
        cursor.execute(
            "SELECT id FROM devices WHERE hioki_id = ? AND device_type = 'w610'",
            (hioki_id,),
        )
        device = cursor.fetchone()
        device_id = device[0] if device else None
        conn.commit()
    return device_id


def get_devices(bridge_id=None):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        if bridge_id:
            cursor.execute(
                """
                SELECT d.*, b.name as bridge_name, b.ip as bridge_ip
                FROM devices d
                JOIN bridges b ON d.bridge_id = b.id
                WHERE d.bridge_id = ?
                ORDER BY d.hioki_id
                """,
                (bridge_id,),
            )
        else:
            cursor.execute(
                """
                SELECT d.*, b.name as bridge_name, b.ip as bridge_ip
                FROM devices d
                LEFT JOIN bridges b ON d.bridge_id = b.id
                ORDER BY b.name, d.hioki_id
                """
            )
        devices = [dict(row) for row in cursor.fetchall()]
        for device in devices:
            if "testing" not in device:
                device["testing"] = 0
        return devices


def update_device_reading(hioki_id, data):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE devices
            SET last_reading = ?, last_update = ?, status = 'active'
            WHERE hioki_id = ?
            """,
            (data["value"], data["timestamp"], hioki_id),
        )
        cursor.execute(
            "SELECT id, device_type FROM devices WHERE hioki_id = ?",
            (hioki_id,),
        )
        device = cursor.fetchone()
        device_id = device["id"] if device else None
        device_type = device["device_type"] if device else None
        if device_id is not None:
            cursor.execute(
                "INSERT INTO device_history (device_id, device_type, data) VALUES (?, ?, ?)",
                (device_id, device_type or "unknown", json.dumps(data)),
            )
        conn.commit()
    return device_id


def find_bridge_by_ip_port(ip, port):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            "SELECT * FROM bridges WHERE ip = ? AND port = ?",
            (ip, port),
        )
        bridge = cursor.fetchone()
        return dict(bridge) if bridge else None


def add_device_record(bridge_id, port, hioki_id):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT id FROM devices WHERE bridge_id = ? AND hioki_id = ?",
            (bridge_id, hioki_id),
        )
        device = cursor.fetchone()
        if not device:
            cursor.execute(
                "INSERT INTO devices (bridge_id, port, hioki_id) VALUES (?, ?, ?)",
                (bridge_id, port, hioki_id),
            )
            conn.commit()
            return cursor.lastrowid
        return None


def remove_device_by_hioki_id(hioki_id):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM devices WHERE hioki_id = ?", (hioki_id,))
        conn.commit()
        return cursor.rowcount > 0


def get_serial_devices():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT * FROM devices
            WHERE device_type = 'serial'
            ORDER BY hioki_id
            """
        )
        return [dict(row) for row in cursor.fetchall()]


def parse_timestamp(ts):
    if not ts:
        return None
    try:
        return datetime.fromisoformat(ts)
    except Exception:
        try:
            return datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None


def get_w610_devices():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT * FROM devices
            WHERE device_type = 'w610'
            ORDER BY hioki_id
            """
        )
        devices = [dict(row) for row in cursor.fetchall()]

    return devices


def add_custom_device(name, ip, port, in_protocol, in_param, out_protocol, out_param):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO custom_devices (name, ip, port, in_protocol, in_param, out_protocol, out_param)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            (name, ip, port, in_protocol, in_param, out_protocol, out_param),
        )
        conn.commit()
        return cursor.lastrowid


def get_custom_devices():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM custom_devices ORDER BY name")
        return [dict(row) for row in cursor.fetchall()]


def update_device_testing_status(device_id, testing):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE devices SET testing = ? WHERE id = ?",
        (1 if testing else 0, device_id),
    )
    cursor.execute("SELECT hioki_id FROM devices WHERE id = ?", (device_id,))
    device = cursor.fetchone()
    conn.commit()
    conn.close()
    return device["hioki_id"] if device else None



def add_device_history(device_id, device_type, data):
    """Insert a history entry for a device."""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO device_history (device_id, device_type, data) VALUES (?, ?, ?)",
            (device_id, device_type, json.dumps(data)),
        )
        conn.commit()


def get_device_history(device_id, limit=100):
    """Retrieve history entries for a device ordered by newest first."""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT id, device_id, device_type, data, timestamp
            FROM device_history
            WHERE device_id = ?
            ORDER BY timestamp DESC
            LIMIT ?
            """,
            (device_id, limit),
        )
        rows = cursor.fetchall()
        history = []
        for row in rows:
            try:
                payload = json.loads(row["data"])
            except Exception:
                payload = row["data"]
            history.append(
                {
                    "id": row["id"],
                    "device_id": row["device_id"],
                    "device_type": row["device_type"],
                    "data": payload,
                    "timestamp": row["timestamp"],
                }
            )
        return history

# ---------------------------------------------------------------------------
# Ultrasonic Welders


def add_welder(raspi_name, ip, port=8080):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO welders (raspi_name, ip, port) VALUES (?, ?, ?)",
            (raspi_name, ip, port),
        )
        conn.commit()
        return cursor.lastrowid


def get_welders():
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM welders ORDER BY raspi_name")
        return [dict(row) for row in cursor.fetchall()]


def get_welder_by_id(welder_id):
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM welders WHERE id = ?", (welder_id,))
        result = cursor.fetchone()
        return dict(result) if result else None


def set_welder_name(welder_id, name):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE welders SET welder_name = ? WHERE id = ?",
            (name, welder_id),
        )
        conn.commit()


def update_welder_status(welder_id, status):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE welders SET status = ?, last_update = CURRENT_TIMESTAMP WHERE id = ?",
            (status, welder_id),
        )
        conn.commit()


def update_welder_data(welder_id, data):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE welders SET last_reading = ?, last_update = CURRENT_TIMESTAMP WHERE id = ?",
            (json.dumps(data) if isinstance(data, dict) else data, welder_id),
        )
        conn.commit()


def remove_welder(welder_id):
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM welders WHERE id = ?", (welder_id,))
        conn.commit()
        return cursor.rowcount > 0


def get_last_reading(entity_type, identifier):
    """Retrieve the most recent reading for a device.

    Args:
        entity_type (str): Type of device (hioki, cts, welder/ultrasonic).
        identifier (str): Identifier for the device. For Hioki this is the
            ``hioki_id``; for CTS and welders this may be either the numeric
            ``id`` or a user supplied name.

    Returns:
        dict | None: Dictionary containing the reading data and timestamp or
            ``None`` if the device/reading could not be found.
    """

    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if entity_type == "hioki":
            cursor.execute(
                "SELECT last_reading, last_update FROM devices WHERE hioki_id = ?",
                (identifier,),
            )
            row = cursor.fetchone()
            if row:
                return {
                    "entity_type": "hioki",
                    "device_id": identifier,
                    "impedance": row["last_reading"],
                    "timestamp": row["last_update"],
                }

        elif entity_type == "cts":
            cursor.execute(
                """
                SELECT testpressure, pressureloss, last_update
                FROM cts
                WHERE id = ? OR cts_id = ?
                """,
                (identifier, identifier),
            )
            row = cursor.fetchone()
            if row:
                return {
                    "entity_type": "cts",
                    "device_id": identifier,
                    "testpressure": row["testpressure"],
                    "pressureloss": row["pressureloss"],
                    "timestamp": row["last_update"],
                }

        elif entity_type in ("welder", "ultrasonic"):
            cursor.execute(
                """
                SELECT last_reading, last_update
                FROM welders
                WHERE id = ? OR raspi_name = ? OR welder_name = ?
                """,
                (identifier, identifier, identifier),
            )
            row = cursor.fetchone()
            if row:
                try:
                    data = json.loads(row["last_reading"]) if row["last_reading"] else {}
                except Exception:
                    data = {"raw": row["last_reading"]}
                result = {
                    "entity_type": "ultrasonic",
                    "device_id": identifier,
                    "timestamp": row["last_update"],
                }
                if isinstance(data, dict):
                    result.update(data)
                else:
                    result["last_reading"] = data
                return result

        return None

