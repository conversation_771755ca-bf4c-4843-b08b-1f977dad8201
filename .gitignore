# Python bytecode and cache
__pycache__/
*.py[cod]
*$py.class
*.so
.pytest_cache/
.coverage
htmlcov/
.tox/
venv/
hioki-dashboard/venv/
# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
/hioki-mqtt-bridge/
/Bridge-Client/venv/
# Virtual environments - catch all variations
*venv*/
*env*/
.env*/
*ENV*/
py*/
.python-version
*virtualenv*/
.venv*/
hioki-dashboard/py311/
venv/
hioki-dashboard/venv/
/hioki-dashboard/dashboard.db

# IDE files
.idea/
.vscode/
*.swp
*.swo
*.sublime-*
.project
.pydevproject
.settings
*.code-workspace
*.iml

# OS specific files
.DS_Store
Thumbs.db
*.lnk
Desktop.ini
$RECYCLE.BIN/
.directory

# Logs and databases
logs/
*.log
*.sqlite3*
*.db
*.sqlite
*.bak
*.dump

# Environment variables and secrets
.env*
*.env
.secret*
config_local.py
config_dev.py
*.pem
*.key
credentials.json

# Compiled files
*.pyc
*.pyo
*.pyd
*.c
*.so
*.dll
*.dylib
/hioki-mqtt-bridge/src/__pycache__/

# Temporary files
temp/
tmp/
.tmp/
*~
*.swp
*.swo
.cache/
.hypothesis/

# Local development files
local_settings.py
docker-compose.override.yml
override.yml

# Build and documentation
docs/_build/
site/
.doctrees/
_build/
build/
dist/

# Test files
.coverage
coverage.xml
*.cover
.pytest_cache/
nosetests.xml

