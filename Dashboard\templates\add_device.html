{% extends 'base.html' %}

{% block title %}Add Device - HexMES Wireless Admin{% endblock %}

{% block content %}
<div class="row mb-4">
	<div class="col">
		<h1 class="display-5">
			<i class="bi bi-plus-circle me-2"></i>Add Device
		</h1>
		<p class="lead">Select device type and fill out the required details.</p>
	</div>
</div>

<div class="row mb-3">
	<div class="col-md-6">
		<label for="deviceType" class="form-label">Device Type</label>
		<select id="deviceType" class="form-select">
			<option value="w610" selected>Hioki W610 Device</option>
			<option value="mqtt">Hioki (MQTT Bridge - Deprecated)</option>
			<option value="cts">CTS Tester</option>
			<option value="ultrasonic">Ultrasonic Welder</option>
			<option value="dickson">Dickson Environmental Monitor</option>
			<option value="other">Other/Custom</option>
		</select>
	</div>
</div>

<div class="row">
	<div class="col-md-8">
		<!-- MQTT Bridge Form -->
		<div id="mqttForm" class="d-none">
			<div class="card">
				<div class="card-body">
					<form method="POST" action="{{ url_for('add_device_route') }}">
						<input type="hidden" name="entity_type" value="hioki">
						<input type="hidden" name="connection_method" value="bridge">

						<div class="mb-3">
							<label for="name" class="form-label">PC Name</label>
							<input type="text" class="form-control" id="name" name="name" required
								placeholder="e.g., Line 1 ABAY">
							<div class="form-text">A descriptive name for this PC</div>
						</div>

						<div class="mb-3">
							<label for="ip" class="form-label">IP Address</label>
							<input type="text" class="form-control" id="ip" name="ip" required
								placeholder="e.g., *************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
							<div class="form-text">The IP address of the PC running Hioki MQTT Bridge</div>
						</div>

						<div class="mb-3">
							<label for="port" class="form-label">Port</label>
							<input type="number" class="form-control" id="port" name="port" value="5000" min="1"
								max="65535">
							<div class="form-text">The port number of the Hioki MQTT Bridge (default: 5000)</div>
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add Bridge</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- W610 Device -->
		<div id="w610Form">
			<div class="card">
				<div class="card-body">
					<div class="alert alert-info">
						<i class="bi bi-info-circle me-2"></i>
						<strong>Note:</strong> W610 devices will connect TO this dashboard on port 15000.
						Make sure your W610 is configured in transparent mode and pointed to this server's IP address.
					</div>

					<form method="POST" action="{{ url_for('add_w610_route') }}">
						<div class="mb-3">
							<label for="w610_name" class="form-label">Hioki ID</label>
							<input type="text" class="form-control" id="w610_name" name="name" required
								placeholder="e.g., H012" pattern="H\d{3}"
								title="Hioki ID must be in format H### (e.g., H001, H012)">
							<div class="form-text">The ID listed on the device - used for MQTT topics</div>
						</div>

						<div class="mb-3">
							<label for="w610_ip" class="form-label">W610 IP Address</label>
							<input type="text" class="form-control" id="w610_ip" name="ip" required
								placeholder="e.g., ************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
							<div class="form-text">
								IP address of the W610 module. This must match the IP that will connect to the
								dashboard.
								Configure your W610 to connect to <strong>{{ request.host.split(':')[0]
									}}:15000</strong>
							</div>
						</div>

						<div class="alert alert-warning">
							<strong>W610 Configuration Required:</strong>
							<ol class="mb-0 mt-2">
								<li>Set W610 to <strong>Transparent Mode</strong></li>
								<li>Configure W610 to connect to <code>{{ request.host.split(':')[0] }}:15000</code>
								</li>
								<li>Ensure the W610 can reach this dashboard server</li>
								<li>The IP address entered above must match the W610's actual IP</li>
							</ol>
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add W610 Device</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- CTS Tester -->
		<div id="ctsForm" class="d-none">
			<div class="card">
				<div class="card-body">
					<div class="alert alert-info">
						<i class="bi bi-info-circle me-2"></i>
						<strong>CTS Connection:</strong> The dashboard will connect to the CTS tester via telnet/TCP and
						send command "4" to start listening to the data stream.
					</div>

					<form method="POST" action="{{ url_for('add_cts_route') }}">
						<div class="mb-3">
							<label for="cts_name" class="form-label">CTS Name</label>
							<input type="text" class="form-control" id="cts_name" name="name" required
								placeholder="e.g., L1M2, L2ENC">
							<div class="form-text">The name of the CTS Tester</div>
						</div>

						<div class="mb-3">
							<label for="cts_ip" class="form-label">CTS IP Address *</label>
							<input type="text" class="form-control" id="cts_ip" name="ip" required
								placeholder="e.g., *************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
							<div class="form-text">The IP address of the CTS tester for TCP connection</div>
						</div>

						<div class="mb-3">
							<label for="cts_port" class="form-label">TCP Port</label>
							<input type="number" class="form-control" id="cts_port" name="port" value="23" min="1"
								max="65535">
							<div class="form-text">The TCP port number for the CTS tester (default: 23)</div>
						</div>

						<div class="mb-3">
							<label for="cts_id" class="form-label">CTS ID *</label>
							<input type="text" class="form-control" id="cts_id" name="cts_id" required
								placeholder="e.g., L1M2, ENC01">
							<div class="form-text">Identifier used in MQTT topics</div>
						</div>

						<div class="mb-3">
							<label class="form-label">CTS Type *</label>
							<div>
								<div class="form-check form-check-inline">
									<input class="form-check-input" type="radio" name="cts_type" id="ctsManifold"
										value="manifold" required checked>
									<label class="form-check-label" for="ctsManifold">Manifold</label>
								</div>
								<div class="form-check form-check-inline">
									<input class="form-check-input" type="radio" name="cts_type" id="ctsEnclosure"
										value="enclosure" required>
									<label class="form-check-label" for="ctsEnclosure">Enclosure</label>
								</div>
							</div>
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add CTS Tester</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- Ultrasonic Welder -->
		<div id="ultrasonicForm" class="d-none">
			<div class="card">
				<div class="card-body">
					<div class="alert alert-info">
						<i class="bi bi-info-circle me-2"></i>
						<strong>Welder Connection:</strong> The dashboard will monitor the ultrasonic welder via HTTP
						polling and MQTT subscription.
					</div>

					<form method="POST" action="{{ url_for('add_ultrasonic_route') }}">
						<div class="mb-3">
							<label for="ultra_welder_name" class="form-label">Welder Name *</label>
							<input type="text" class="form-control" id="ultra_welder_name" name="welder_name" required
								placeholder="e.g., USW1, USW2, WELDER001">
							<div class="form-text">Name used in MQTT topics
								(nomuda/gvl/tools/GVB-BRANSON/{welder_name}/result/...)</div>
						</div>

						<div class="mb-3">
							<label for="ultra_raspi" class="form-label">Raspberry Pi Name *</label>
							<input type="text" class="form-control" id="ultra_raspi" name="raspi_name" required
								placeholder="e.g., RaspiUW01, WelderPi-Line2">
							<div class="form-text">Name of the Raspberry Pi controlling the welder</div>
						</div>

						<div class="mb-3">
							<label for="ultra_ip" class="form-label">IP Address *</label>
							<input type="text" class="form-control" id="ultra_ip" name="ip" required
								placeholder="e.g., *************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
							<div class="form-text">IP address of the Raspberry Pi</div>
						</div>

						<div class="mb-3">
							<label for="ultra_port" class="form-label">Port</label>
							<input type="number" class="form-control" id="ultra_port" name="port" value="8080" min="1"
								max="65535">
							<div class="form-text">HTTP port for status monitoring (default: 8080)</div>
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add Ultrasonic Welder</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- Dickson Environmental Monitor -->
		<div id="dicksonForm" class="d-none">
			<div class="card">
				<div class="card-body">
					<div class="alert alert-info">
						<i class="bi bi-thermometer-half me-2"></i>
						<strong>Environmental Monitoring:</strong> Monitor temperature and humidity with Dickson WiFi
						devices.
					</div>

					<form method="POST" action="{{ url_for('add_dickson_route') }}">
						<div class="mb-3">
							<label for="dickson_name" class="form-label">Device Name *</label>
							<input type="text" class="form-control" id="dickson_name" name="name" required
								placeholder="e.g., Production Floor Monitor, Cold Storage 1">
							<div class="form-text">Descriptive name for this environmental monitor</div>
						</div>

						<div class="mb-3">
							<label for="dickson_location" class="form-label">Location *</label>
							<input type="text" class="form-control" id="dickson_location" name="location" required
								placeholder="e.g., Production Floor Zone A, Cold Storage Room 1">
							<div class="form-text">Physical location of the device</div>
						</div>

						<div class="mb-3">
							<label for="dickson_ip" class="form-label">IP Address *</label>
							<input type="text" class="form-control" id="dickson_ip" name="ip_address" required
								placeholder="e.g., *************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
							<div class="form-text">IP address of the Dickson device</div>
						</div>

						<div class="mb-3">
							<label for="dickson_location_type" class="form-label">Location Type *</label>
							<select class="form-select" id="dickson_location_type" name="location_type" required>
								<option value="factory_floor" selected>Factory Floor (18-27°C)</option>
								<option value="cold_storage">Cold Storage (4-8°C)</option>
								<option value="custom">Custom Range</option>
							</select>
							<div class="form-text">Select the appropriate temperature range for this location</div>
						</div>

						<div id="customTempRange" class="d-none">
							<div class="row">
								<div class="col-md-6">
									<div class="mb-3">
										<label for="dickson_temp_min" class="form-label">Minimum Temperature
											(°C)</label>
										<input type="number" class="form-control" id="dickson_temp_min" name="temp_min"
											step="0.1" placeholder="e.g., 15.0">
									</div>
								</div>
								<div class="col-md-6">
									<div class="mb-3">
										<label for="dickson_temp_max" class="form-label">Maximum Temperature
											(°C)</label>
										<input type="number" class="form-control" id="dickson_temp_max" name="temp_max"
											step="0.1" placeholder="e.g., 30.0">
									</div>
								</div>
							</div>
						</div>

						<div class="alert alert-warning">
							<strong>Alert Thresholds:</strong>
							<ul class="mb-0">
								<li><strong>Temperature:</strong> Alarms when outside selected range</li>
								<li><strong>Humidity:</strong> Warning below 40%, alarm below 20% or above 50%</li>
							</ul>
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add Dickson Device</button>
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- Other/Custom -->
		<div id="otherForm" class="d-none">
			<div class="card">
				<div class="card-body">
					<form method="POST" action="{{ url_for('add_custom_route') }}">
						<div class="mb-3">
							<label for="other_name" class="form-label">Device Name *</label>
							<input type="text" class="form-control" id="other_name" name="name" required
								placeholder="e.g., CustomTester01">
						</div>

						<div class="mb-3">
							<label for="other_ip" class="form-label">IP Address *</label>
							<input type="text" class="form-control" id="other_ip" name="ip" required
								placeholder="e.g., *************"
								pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
						</div>

						<div class="mb-3">
							<label for="other_port" class="form-label">Port</label>
							<input type="number" class="form-control" id="other_port" name="port" value="80" min="1"
								max="65535">
						</div>

						<div class="mb-3">
							<label for="in_protocol" class="form-label">Incoming Protocol *</label>
							<select id="in_protocol" name="in_protocol" class="form-select" required>
								<option value="webhook">Webhook/API</option>
								<option value="hyperterminal">Hyperterminal</option>
								<option value="mqtt">MQTT</option>
								<option value="redis">Redis</option>
							</select>
						</div>

						<div class="mb-3 d-none" id="in_mqtt_group">
							<label for="in_mqtt_topic" class="form-label">MQTT Topic</label>
							<input type="text" class="form-control" id="in_mqtt_topic" name="in_param"
								placeholder="e.g., devices/custom01/data">
						</div>

						<div class="mb-3 d-none" id="in_redis_group">
							<label for="in_redis_channel" class="form-label">Redis Channel</label>
							<input type="text" class="form-control" id="in_redis_channel" name="in_param"
								placeholder="e.g., custom_device_data">
						</div>

						<div class="mb-3">
							<label for="out_protocol" class="form-label">Output Protocol *</label>
							<select id="out_protocol" name="out_protocol" class="form-select" required>
								<option value="webhook">Webhook/API</option>
								<option value="mqtt">MQTT</option>
								<option value="redis">Redis</option>
							</select>
						</div>

						<div class="mb-3 d-none" id="out_http_group">
							<label for="out_http" class="form-label">HTTP Route</label>
							<input type="text" class="form-control" id="out_http" name="out_param"
								placeholder="e.g., /api/results">
						</div>

						<div class="mb-3 d-none" id="out_mqtt_group">
							<label for="out_mqtt_topic" class="form-label">MQTT Topic</label>
							<input type="text" class="form-control" id="out_mqtt_topic" name="out_param"
								placeholder="e.g., results/custom01">
						</div>

						<div class="mb-3 d-none" id="out_redis_group">
							<label for="out_redis_channel" class="form-label">Redis Channel</label>
							<input type="text" class="form-control" id="out_redis_channel" name="out_param"
								placeholder="e.g., results_channel">
						</div>

						<div class="d-flex justify-content-between">
							<a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
							<button type="submit" class="btn btn-primary">Add Custom Device</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-4">
		<div class="card">
			<div class="card-header">
				<h5 class="mb-0">Device Information</h5>
			</div>
			<div class="card-body">
				<div id="deviceInfo">
					<div id="w610Info">
						<h6>Hioki W610 Device</h6>
						<p>W610 devices connect directly to this dashboard server on port 15000.</p>
						<ul>
							<li>Configure W610 in transparent mode</li>
							<li>Point W610 to this server's IP:15000</li>
							<li>Enter the W610's IP address</li>
							<li>Provide the Hioki ID (e.g., H001)</li>
						</ul>
					</div>
					<div id="mqttInfo" class="d-none">
						<h6>MQTT Bridge (Deprecated)</h6>
						<p>Legacy connection through PC-based MQTT bridge.</p>
						<p><strong>Recommended:</strong> Use W610 devices instead.</p>
					</div>
					<div id="ctsInfo" class="d-none">
						<h6>CTS Tester</h6>
						<p>Connects via TCP/Telnet to CTS testers.</p>
						<ul>
							<li>Requires IP address of CTS tester</li>
							<li>Uses TCP port (usually 23)</li>
							<li>Sends command "4" to start data stream</li>
							<li>Choose Manifold or Enclosure type</li>
						</ul>
					</div>
					<div id="ultrasonicInfo" class="d-none">
						<h6>Ultrasonic Welder</h6>
						<p>Monitors ultrasonic welders via HTTP and MQTT.</p>
						<ul>
							<li>Requires Raspberry Pi IP address</li>
							<li>HTTP port for status monitoring</li>
							<li>Automatically subscribes to MQTT topics</li>
						</ul>
					</div>
					<div id="otherInfo" class="d-none">
						<h6>Custom Device</h6>
						<p>Configure custom devices with specific protocols.</p>
						<ul>
							<li>Flexible protocol configuration</li>
							<li>Webhook, MQTT, or Redis support</li>
							<li>Custom input/output parameters</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	document.addEventListener('DOMContentLoaded', function () {
		const deviceTypeSelect = document.getElementById('deviceType');

		// Set up the device type selector
		deviceTypeSelect.addEventListener('change', function () {
			// Hide all forms
			['mqttForm', 'w610Form', 'ctsForm', 'ultrasonicForm', 'dicksonForm', 'otherForm'].forEach(id => {
				const el = document.getElementById(id);
				if (el) el.classList.add('d-none');
			});

			// Hide all info panels
			['mqttInfo', 'w610Info', 'ctsInfo', 'ultrasonicInfo', 'dicksonInfo', 'otherInfo'].forEach(id => {
				const el = document.getElementById(id);
				if (el) el.classList.add('d-none');
			});

			let value = this.value;

			// Show deprecation warning for MQTT
			if (value === 'mqtt') {
				const proceed = confirm('MQTT Bridge devices are deprecated. Use W610 devices instead. Continue anyway?');
				if (!proceed) {
					this.value = 'w610';
					value = 'w610';
				}
			}

			// Show selected form
			const selectedForm = {
				'mqtt': 'mqttForm',
				'w610': 'w610Form',
				'cts': 'ctsForm',
				'ultrasonic': 'ultrasonicForm',
				'dickson': 'dicksonForm',
				'other': 'otherForm'
			}[value];

			if (selectedForm) {
				document.getElementById(selectedForm).classList.remove('d-none');
			}

			// Show selected info
			const selectedInfo = {
				'mqtt': 'mqttInfo',
				'w610': 'w610Info',
				'cts': 'ctsInfo',
				'ultrasonic': 'ultrasonicInfo',
				'dickson': 'dicksonInfo',
				'other': 'otherInfo'
			}[value];

			if (selectedInfo) {
				document.getElementById(selectedInfo).classList.remove('d-none');
			}
		});

		// Trigger initial display
		deviceTypeSelect.dispatchEvent(new Event('change'));

		// Incoming protocol handler
		const inProtocol = document.getElementById('in_protocol');
		const inMqttGroup = document.getElementById('in_mqtt_group');
		const inRedisGroup = document.getElementById('in_redis_group');

		function updateIncoming() {
			if (!inProtocol) return;
			const val = inProtocol.value;
			if (inMqttGroup) inMqttGroup.classList.toggle('d-none', val !== 'mqtt');
			if (inRedisGroup) inRedisGroup.classList.toggle('d-none', val !== 'redis');
		}

		if (inProtocol) {
			inProtocol.addEventListener('change', updateIncoming);
			updateIncoming();
		}

		// Outgoing protocol handler
		const outProtocol = document.getElementById('out_protocol');
		const outHttpGroup = document.getElementById('out_http_group');
		const outMqttGroup = document.getElementById('out_mqtt_group');
		const outRedisGroup = document.getElementById('out_redis_group');

		function updateOutgoing() {
			if (!outProtocol) return;
			const val = outProtocol.value;
			if (outHttpGroup) outHttpGroup.classList.toggle('d-none', val !== 'webhook');
			if (outMqttGroup) outMqttGroup.classList.toggle('d-none', val !== 'mqtt');
			if (outRedisGroup) outRedisGroup.classList.toggle('d-none', val !== 'redis');
		}

		if (outProtocol) {
			outProtocol.addEventListener('change', updateOutgoing);
			updateOutgoing();
		}

		// Handle custom temperature range for Dickson devices
		const dicksonLocationType = document.getElementById('dickson_location_type');
		const customTempRange = document.getElementById('customTempRange');

		if (dicksonLocationType && customTempRange) {
			dicksonLocationType.addEventListener('change', function () {
				if (this.value === 'custom') {
					customTempRange.classList.remove('d-none');
				} else {
					customTempRange.classList.add('d-none');
				}
			});
		}
	});
</script>
{% endblock %}