"""
Unified service integration layer.
Provides backward-compatible functions while using the unified database structure.
Update your app.py imports to use this module.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .unified_db_service import unified_service
from .result_processors import ResultProcessorFactory
from .unified_mqtt_service import unified_mqtt_service

logger = logging.getLogger(__name__)


class UnifiedServiceAdapter:
    """Adapter to provide backward compatibility with existing code."""

    def __init__(self):
        self.unified_service = unified_service
        self.processor_factory = ResultProcessorFactory()

    # Bridge management (backward compatibility)
    def get_bridges(self) -> List[Dict]:
        """Get all bridge devices."""
        bridges = self.unified_service.get_devices_by_type("bridge")

        # Convert to legacy format
        legacy_bridges = []
        for bridge in bridges:
            config = json.loads(bridge.get("config", "{}"))
            legacy_bridges.append(
                {
                    "id": bridge["id"],
                    "name": bridge["name"],
                    "ip": bridge["ip"],
                    "port": bridge["port"],
                    "status": bridge["status"],
                    "last_seen": bridge["last_update"],
                    "conn_method": "bridge",
                }
            )

        return legacy_bridges

    def get_bridge(self, bridge_id: int) -> Optional[Dict]:
        """Get a specific bridge."""
        bridges = self.get_bridges()
        for bridge in bridges:
            if bridge["id"] == bridge_id:
                return bridge
        return None

    def add_bridge(self, name: str, ip: str, port: int = 5000) -> int:
        """Add a new bridge."""
        with self.unified_service.get_db_connection() as conn:
            cursor = conn.cursor()

            bridge_config = {"bridge_type": "mqtt", "legacy_bridge": True}

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, config)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                ("bridge", f"bridge_{name}", name, ip, port, json.dumps(bridge_config)),
            )

            conn.commit()
            return cursor.lastrowid

    def update_bridge_status(self, bridge_id: int, status: str):
        """Update bridge status."""
        with self.unified_service.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                UPDATE unified_devices 
                SET status = ?, last_update = CURRENT_TIMESTAMP 
                WHERE id = ? AND entity_type = 'bridge'
            """,
                (status, bridge_id),
            )
            conn.commit()

    # Device management
    def get_devices(self, bridge_id: int = None) -> List[Dict]:
        """Get devices, optionally filtered by bridge."""
        if bridge_id:
            # Get devices for specific bridge
            with self.unified_service.get_db_connection() as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT ud.*, b.name as bridge_name, b.ip as bridge_ip
                    FROM unified_devices ud
                    LEFT JOIN unified_devices b ON JSON_EXTRACT(ud.config, '$.bridge_id') = b.id
                    WHERE JSON_EXTRACT(ud.config, '$.bridge_id') = ? 
                    AND ud.entity_type LIKE 'hioki_%'
                    ORDER BY ud.name
                """,
                    (bridge_id,),
                )

                devices = [
                    self._convert_device_to_legacy(dict(row))
                    for row in cursor.fetchall()
                ]
        else:
            # Get all Hioki devices
            hioki_devices = []
            for entity_type in [
                "hioki_bridge",
                "hioki_w610",
                "hioki_serial",
                "hioki_http",
            ]:
                devices = self.unified_service.get_devices_by_type(entity_type)
                hioki_devices.extend(devices)

            devices = [
                self._convert_device_to_legacy(device) for device in hioki_devices
            ]

        return devices

    def get_serial_devices(self) -> List[Dict]:
        """Get serial devices."""
        devices = self.unified_service.get_devices_by_type("hioki_serial")
        return [self._convert_device_to_legacy(device) for device in devices]

    def get_w610_devices(self) -> List[Dict]:
        """Get W610 devices."""
        devices = self.unified_service.get_devices_by_type("hioki_w610")
        return [self._convert_device_to_legacy(device) for device in devices]

    def get_cts(self) -> List[Dict]:
        """Get CTS devices."""
        devices = self.unified_service.get_devices_by_type("cts")
        return [self._convert_cts_to_legacy(device) for device in devices]

    def get_welders(self) -> List[Dict]:
        """Get welder devices."""
        devices = self.unified_service.get_devices_by_type("welder")
        return [self._convert_welder_to_legacy(device) for device in devices]

    def get_custom_devices(self) -> List[Dict]:
        """Get custom devices."""
        devices = self.unified_service.get_devices_by_type("custom")
        return [self._convert_custom_to_legacy(device) for device in devices]

    def _convert_device_to_legacy(self, device: Dict) -> Dict:
        """Convert unified device to legacy format."""
        config = json.loads(device.get("config", "{}"))
        last_reading = (
            json.loads(device.get("last_reading", "{}"))
            if device.get("last_reading")
            else {}
        )

        return {
            "id": device["id"],
            "hioki_id": device["device_id"],
            "name": device["name"],
            "ip": device["ip"],
            "port": device["port"],
            "status": device["status"],
            "last_reading": last_reading.get("impedance")
            or device.get("last_raw_data"),
            "last_update": device["last_update"],
            "testing": device["testing"],
            "device_type": config.get("device_type", "unknown"),
            "tester_type": config.get("tester_type", "hioki"),
            "bridge_id": config.get("bridge_id"),
            "bridge_name": config.get("bridge_name"),
            "bridge_ip": config.get("bridge_ip"),
        }

    def _convert_cts_to_legacy(self, device: Dict) -> Dict:
        """Convert CTS device to legacy format."""
        config = json.loads(device.get("config", "{}"))
        last_reading = (
            json.loads(device.get("last_reading", "{}"))
            if device.get("last_reading")
            else {}
        )

        return {
            "id": device["id"],
            "name": device["name"],
            "ip": device["ip"],
            "port": device["port"],
            "status": device["status"],
            "cts_id": config.get("cts_id", device["device_id"]),
            "cts_type": config.get("cts_type", "manifold"),
            "testpressure": last_reading.get("testpressure"),
            "pressureloss": last_reading.get("pressureloss"),
            "last_reading": device.get("last_raw_data"),
            "last_update": device["last_update"],
        }

    def _convert_welder_to_legacy(self, device: Dict) -> Dict:
        """Convert welder device to legacy format."""
        config = json.loads(device.get("config", "{}"))

        return {
            "id": device["id"],
            "raspi_name": config.get("raspi_name", device["name"]),
            "welder_name": config.get("welder_name", device["name"]),
            "ip": device["ip"],
            "port": device["port"],
            "status": device["status"],
            "last_reading": device.get("last_raw_data"),
            "last_update": device["last_update"],
        }

    def _convert_custom_to_legacy(self, device: Dict) -> Dict:
        """Convert custom device to legacy format."""
        config = json.loads(device.get("config", "{}"))

        return {
            "id": device["id"],
            "name": device["name"],
            "ip": device["ip"],
            "port": device["port"],
            "in_protocol": config.get("in_protocol"),
            "in_param": config.get("in_param"),
            "out_protocol": config.get("out_protocol"),
            "out_param": config.get("out_param"),
        }

    # Result processing
    def update_device_reading(
        self, device_id: str, data: Dict, entity_type: str = None
    ) -> Optional[int]:
        """Update device reading using unified system."""
        if not entity_type:
            # Try to determine entity type from device_id format or existing devices
            entity_type = self._determine_entity_type(device_id)

        # Get device config
        devices = self.unified_service.get_devices_by_type(entity_type)
        device_config = None
        for device in devices:
            if device["device_id"] == device_id:
                device_config = json.loads(device.get("config", "{}"))
                break

        # Determine raw and processed data
        raw_data = data.get("raw_value") or data.get("value") or str(data)
        processed_result = data.get("processed_data")

        # If processed data not provided, process the raw data
        if not processed_result:
            processed_result = self.processor_factory.process_result(
                entity_type=entity_type,
                device_id=device_id,
                raw_data=raw_data,
                device_config=device_config,
            )

        if not processed_result:
            return None

        result_id = None
        try:
            # Store result in local database
            result_id = self.unified_service.store_device_result(
                device_identifier=device_id,
                entity_type=entity_type,
                raw_data=raw_data,
                parsed_data=processed_result,
            )
        except Exception as e:
            logger.error(f"Failed to store result for {device_id}: {e}")

        # Publish to MQTT even if database storage fails
        logger.debug(f"Attempting MQTT publish for {entity_type} device {device_id}")
        if unified_mqtt_service.mqtt_client:
            logger.debug(
                f"MQTT client available, publishing result: {processed_result}"
            )
            unified_mqtt_service.publish_result(
                entity_type=entity_type,
                device_id=device_id,
                result=processed_result,
                device_config=device_config,
            )
        else:
            logger.warning(
                f"MQTT client not available for {entity_type} device {device_id}"
            )

        return result_id

    def update_cts_reading(self, cts_id: int, data: Dict):
        """Update CTS reading."""
        # Find the CTS device
        cts_devices = self.get_cts()
        cts_device = None
        for device in cts_devices:
            if device["id"] == cts_id:
                cts_device = device
                break

        if not cts_device:
            logger.warning(f"CTS device {cts_id} not found")
            return

        device_id = cts_device["cts_id"] or cts_device["name"]
        raw_line = data.get("raw", "")

        # Process CTS data
        processed_result = self.processor_factory.process_result(
            entity_type="cts", device_id=device_id, raw_data=raw_line
        )

        if processed_result:
            self.unified_service.store_device_result(
                device_identifier=device_id,
                entity_type="cts",
                raw_data=raw_line,
                parsed_data=processed_result,
            )

            # Publish CTS results
            if unified_mqtt_service.mqtt_client:
                device_config = {"cts_type": cts_device.get("cts_type", "manifold")}
                unified_mqtt_service.publish_cts_results(
                    device_id=device_id,
                    processed_result=processed_result,
                    device_config=device_config,
                )

    def _determine_entity_type(self, device_id: str) -> str:
        """Try to determine entity type from device ID or existing devices."""
        # Check all entity types to find the device
        for entity_type in [
            "hioki_w610",
            "hioki_bridge",
            "hioki_serial",
            "hioki_http",
            "cts",
            "welder",
            "custom",
        ]:
            devices = self.unified_service.get_devices_by_type(entity_type)
            for device in devices:
                if device["device_id"] == device_id:
                    return entity_type

        # Default to W610 if not found (for backward compatibility)
        return "hioki_w610"

    # Device history
    def get_device_history(self, device_id: int, limit: int = 100) -> List[Dict]:
        """Get device history in legacy format."""
        with self.unified_service.get_db_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get device info first
            cursor.execute(
                "SELECT device_id, entity_type FROM unified_devices WHERE id = ?",
                (device_id,),
            )
            device = cursor.fetchone()

            if not device:
                return []

            # Get results for this device
            results = self.unified_service.get_device_results(
                device_identifier=device["device_id"],
                entity_type=device["entity_type"],
                limit=limit,
            )

            # Convert to legacy format
            history = []
            for result in results:
                try:
                    parsed_data = (
                        json.loads(result["parsed_data"])
                        if result["parsed_data"]
                        else {}
                    )
                except:
                    parsed_data = {}

                history.append(
                    {
                        "id": result["id"],
                        "device_id": device_id,
                        "device_type": device["entity_type"],
                        "data": parsed_data,
                        "timestamp": result["timestamp"],
                    }
                )

            return history

    # Status updates
    def update_device_status_by_hioki_id(
        self, hioki_id: str, status: str
    ) -> Optional[int]:
        """Update device status by Hioki ID."""
        with self.unified_service.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                UPDATE unified_devices 
                SET status = ?, last_update = CURRENT_TIMESTAMP 
                WHERE device_id = ? AND entity_type LIKE 'hioki_%'
            """,
                (status, hioki_id),
            )

            cursor.execute(
                """
                SELECT id FROM unified_devices 
                WHERE device_id = ? AND entity_type LIKE 'hioki_%'
            """,
                (hioki_id,),
            )

            result = cursor.fetchone()
            device_id = result[0] if result else None
            conn.commit()

            return device_id

    def update_cts_status(self, cts_id: int, status: str):
        """Update CTS status."""
        with self.unified_service.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                UPDATE unified_devices 
                SET status = ?, last_update = CURRENT_TIMESTAMP 
                WHERE id = ? AND entity_type = 'cts'
            """,
                (status, cts_id),
            )
            conn.commit()

    def update_welder_status(self, welder_id: int, status: str):
        """Update welder status."""
        with self.unified_service.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                UPDATE unified_devices 
                SET status = ?, last_update = CURRENT_TIMESTAMP 
                WHERE id = ? AND entity_type = 'welder'
            """,
                (status, welder_id),
            )
            conn.commit()

    def update_welder_data(self, welder_id: int, data: Dict):
        """Update welder data."""
        # Process the welder data
        with self.unified_service.get_db_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get welder device
            cursor.execute(
                "SELECT device_id FROM unified_devices WHERE id = ? AND entity_type = 'welder'",
                (welder_id,),
            )
            result = cursor.fetchone()

            if result:
                device_id = result["device_id"]

                # Process and store the data
                raw_data = json.dumps(data) if isinstance(data, dict) else str(data)
                processed_result = self.processor_factory.process_result(
                    entity_type="welder", device_id=device_id, raw_data=raw_data
                )

                if processed_result:
                    self.unified_service.store_device_result(
                        device_identifier=device_id,
                        entity_type="welder",
                        raw_data=raw_data,
                        parsed_data=processed_result,
                    )

    # Convenience methods for API compatibility
    def get_last_reading(self, entity_type: str, identifier: str) -> Optional[Dict]:
        """Get last reading for a device."""
        return self.unified_service.get_last_reading(entity_type, identifier)


# Create global adapter instance
adapter = UnifiedServiceAdapter()

# Export backward-compatible functions
get_bridges = adapter.get_bridges
get_bridge = adapter.get_bridge
add_bridge = adapter.add_bridge
update_bridge_status = adapter.update_bridge_status
get_devices = adapter.get_devices
get_serial_devices = adapter.get_serial_devices
get_w610_devices = adapter.get_w610_devices
get_cts = adapter.get_cts
get_welders = adapter.get_welders
get_custom_devices = adapter.get_custom_devices
update_device_reading = adapter.update_device_reading
update_cts_reading = adapter.update_cts_reading
get_device_history = adapter.get_device_history
update_device_status_by_hioki_id = adapter.update_device_status_by_hioki_id
update_cts_status = adapter.update_cts_status
update_welder_status = adapter.update_welder_status
update_welder_data = adapter.update_welder_data
get_last_reading = adapter.get_last_reading

# Export the unified services for direct access
unified_db_service = unified_service
result_processor_factory = ResultProcessorFactory()
