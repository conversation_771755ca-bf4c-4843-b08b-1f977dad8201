<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hioki Resistance Meter MQTT Bridge">
    <title>{% block title %}Hioki MQTT Bridge{% endblock %}</title>
    <!-- Add favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='favicon.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap">
    {% block extra_css %}{% endblock %}
</head>

<body>
    <div class="app-container">
        <header class="header">
            <div class="header-content">
                <div class="logo-container">
                    <img src="{{ url_for('static', filename='images/Proterra_Logo.png') }}" alt="Proterra Logo"
                        class="proterra-logo">
                    <h1>Hioki MQTT Bridge</h1>
                </div>
                <div class="header-status">
                    <span class="mqtt-status" id="mqttStatus">
                        <span class="status-indicator" id="mqttIndicator"></span>
                        MQTT Status
                    </span>
                </div>
                <div class="header-actions">
                    {% if session.username %}
                    <span class="user-name">{{ session.username }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="button button-secondary">Logout</a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}" class="button button-secondary">Login</a>
                    {% endif %}
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="container">
                {% block content %}{% endblock %}
            </div>
        </main>

        <footer class="footer">
            <div class="footer-content">
                <div class="system-info">
                    <span id="connectedDevices">Connected Devices: </span>
                    <span id="mqttBroker">Broker: vf-gateway-01:1883</span>
                    <span id="serverAddress">Server: {{ server_ip }}:5000</span>
                </div>
                <p class="copyright">&copy; {{ current_year }} Proterra</p>
                <p class="version">Version: {{ version }}</p>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}

    <!-- Error/Success Message -->
    <div class="toast-container" id="toastContainer"></div>
</body>

</html>