{% extends 'base.html' %}
{% block content %}
<div class="dashboard">
    <section class="card" style="max-width:400px;margin:0 auto;">
        <h2>Login</h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
        {% endwith %}
        <form method="post">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <button type="submit" class="button">Login</button>
        </form>
    </section>
</div>
{% endblock %}
