from flask import (
    Blueprint,
    render_template,
    jsonify,
    request,
    current_app,
    session,
    redirect,
    url_for,
    flash,
)
from functools import wraps
import os
import sys

# Ensure the project root is on the Python path so ``ldap_utils`` is importable
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

from ldap_utils import authenticate
import urllib.parse
from src.app import device_manager
from src.services.com_service import COMService
from src.services.db_service import DBService

# Toggle token authentication for API routes. Set the ``AUTH_ENABLED``
# environment variable to ``true`` to enforce authentication again.
AUTH_ENABLED = os.environ.get("AUTH_ENABLED", "false").lower() in (
    "1",
    "true",
    "yes",
    "on",
)

# Create blueprints
main_bp = Blueprint("main", __name__)
api_bp = Blueprint("api", __name__)
auth_bp = Blueprint("auth", __name__)


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get("username"):
            return redirect(url_for("auth.login", next=request.url))
        return f(*args, **kwargs)

    return decorated_function


def api_auth_required(f):
    """Allow access with session login or valid API token."""
    @wraps(f)
    def decorated(*args, **kwargs):
        if not AUTH_ENABLED:
            return f(*args, **kwargs)
        # Check session first
        if session.get("username"):
            return f(*args, **kwargs)

        # Check bearer token
        auth_header = request.headers.get("Authorization", "")
        token = None
        if auth_header.startswith("Bearer "):
            token = auth_header[len("Bearer ") :]
        if not token:
            token = request.args.get("access_token")

        if token:
            db_service = DBService(current_app.config["DB_PATH"])
            username = db_service.validate_api_token(token)
            if username:
                # store for downstream use if needed
                request.api_user = username
                return f(*args, **kwargs)

        return jsonify({"error": "Unauthorized"}), 401

    return decorated


@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    next_url = request.args.get("next")
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")
        if authenticate(username, password):
            session["username"] = username
            return redirect(next_url or url_for("main.index"))
        else:
            flash("Invalid credentials", "danger")
    return render_template("login.html")


@auth_bp.route("/logout")
def logout():
    session.pop("username", None)
    return redirect(url_for("auth.login"))


@api_bp.route("/token", methods=["POST"])
def issue_token():
    """Return an API token given valid credentials."""
    data = request.json or {}
    username = data.get("username")
    password = data.get("password")
    if not username or not password:
        return jsonify({"error": "Username and password required"}), 400
    if not authenticate(username, password):
        return jsonify({"error": "Invalid credentials"}), 401

    db_service = DBService(current_app.config["DB_PATH"])
    token = db_service.create_api_token(username)
    return jsonify({"token": token})



# Main routes
@main_bp.route("/")
@login_required
def index():
    """Render the main dashboard page"""
    return render_template("index.html")


# API routes
@api_bp.route("/get_ports", methods=["GET"])
@api_auth_required
def get_ports():
    """Get available COM ports"""
    # Reuse the existing COM service rather than creating a new instance
    com_service = current_app.config.get("COM_SERVICE") or device_manager.com_service
    ports = com_service.list_ports() if com_service else []
    port_list = [port.device for port in ports]
    return jsonify({"ports": port_list})


@api_bp.route("/mqtt_status", methods=["GET"])
@api_auth_required
def mqtt_status():

    com_service = current_app.config.get("COM_SERVICE") or device_manager.com_service
    if not com_service or not com_service.mqtt_client:
        return jsonify({"connected": False, "message": "MQTT client not initialized"})
    try:
        if com_service.mqtt_client.is_connected():
            return jsonify({"connected": True, "message": "MQTT connected"})
        else:
            return jsonify({"connected": False, "message": "MQTT disconnected"})
    except Exception as e:
        return jsonify({"connected": False, "message": f"Error: {e}"})


@api_bp.route("/devices", methods=["GET"])
@api_auth_required
def get_devices():
    """Get all registered devices"""
    devices = device_manager.get_all_devices()
    device_dict = {dev["port"]: dev for dev in devices}
    return jsonify({"devices": device_dict})


@api_bp.route("/add_device", methods=["POST"])
@api_auth_required
def add_device():
    """Add a new device"""
    data = request.json
    port = data.get("port")
    hioki_id = data.get("hioki_id")

    if not port or not hioki_id:
        return (
            jsonify({"status": "error", "message": "Port and Hioki ID are required"}),
            400,
        )

    success = device_manager.add_device(port, hioki_id)

    if success:
        return jsonify(
            {"status": "success", "message": f"Device {hioki_id} added on {port}"}
        )
    else:
        return (
            jsonify({"status": "error", "message": f"Device already exists on {port}"}),
            400,
        )


@api_bp.route("/remove_device/<path:port>", methods=["DELETE"])
@api_auth_required
def remove_device(port):
    """Remove a device. Handles new port formats from the dashboard."""
    # Normalise the port because the dashboard may send a shortened format
    # e.g. "0" instead of "/dev/ttyACM0" or may lose the leading slash due to
    # URL redirection. Compare against known devices and map to the full port
    # string used internally by the device manager.
    decoded_port = urllib.parse.unquote(port)

    # Build a mapping of known ports
    registered_ports = [d["port"] for d in device_manager.get_all_devices()]

    if decoded_port not in registered_ports:
        for rp in registered_ports:
            if rp.endswith(decoded_port.lstrip("/")):
                decoded_port = rp
                break

    hioki_id = device_manager.remove_device(decoded_port)
    if hioki_id:
        return jsonify(
            {"status": "success", "message": f"Device {hioki_id} removed from {port}"}
        )
    else:
        return (
            jsonify({"status": "error", "message": f"No device found on {port}"}),
            404,
        )


@api_bp.route("/test_mode/<path:port>", methods=["POST"])
@api_auth_required
def set_test_mode(port):
    """Set device test mode.

    Similar to the remove_device route, the dashboard may send shortened
    or URL-normalised port strings (e.g. ``dev/ttyACM0``).  Normalise the
    value so it matches the registered device ports before updating the
    device manager.
    """

    data = request.json
    enable_val = data.get("enable", False)
    if isinstance(enable_val, str):
        enable = enable_val.lower() in ("true", "1", "yes", "on")
    else:
        enable = bool(enable_val)

    decoded_port = urllib.parse.unquote(port)
    registered_ports = [d["port"] for d in device_manager.get_all_devices()]

    if decoded_port not in registered_ports:
        for rp in registered_ports:
            if rp.endswith(decoded_port.lstrip("/")):
                decoded_port = rp
                break

    success = device_manager.set_device_test_mode(decoded_port, enable)

    if success:
        mode = "enabled" if enable else "disabled"
        return jsonify(
            {"status": "success", "message": f"Test mode {mode} for device on {port}"}
        )
    else:
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"Failed to set test mode for device on {port}",
                }
            ),
            400,
        )
