#!/usr/bin/env python3
"""
Migration script to move from legacy device tables to unified structure.
This script should be run once to migrate existing data.
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def backup_database(db_path: str):
    """Create a backup of the database before migration."""
    backup_path = f"{db_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    try:
        import shutil

        shutil.copy2(db_path, backup_path)
        logger.info(f"Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        raise


def verify_legacy_tables_exist(db_path: str) -> bool:
    """Verify that legacy tables exist before migration."""
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()

        # Check for existing tables
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('bridges', 'devices', 'cts', 'welders', 'custom_devices')
        """
        )

        existing_tables = [row[0] for row in cursor.fetchall()]
        logger.info(f"Found legacy tables: {existing_tables}")

        return len(existing_tables) > 0


def init_unified_db(db_path: str):
    """Initialize the unified database structure."""
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()

        # Create unified devices table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS unified_devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,
                device_id TEXT NOT NULL,
                name TEXT NOT NULL,
                ip TEXT,
                port INTEGER,
                status TEXT DEFAULT 'unknown',
                config JSON,
                last_reading JSON,
                last_raw_data TEXT,
                last_update TIMESTAMP,
                testing INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # Create unified results table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS device_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                entity_type TEXT NOT NULL,
                device_identifier TEXT NOT NULL,
                raw_data TEXT,
                parsed_data JSON,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                command_id INTEGER,
                mqtt_topic TEXT,
                FOREIGN KEY (device_id) REFERENCES unified_devices (id),
                FOREIGN KEY (command_id) REFERENCES vf_commands (id)
            )
        """
        )

        # VF commands table (may already exist from VF capture)
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS vf_commands (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_type TEXT NOT NULL,
                device_id TEXT NOT NULL,
                tool_command TEXT,
                work_station_name TEXT,
                person_name TEXT,
                test_item_name TEXT,
                activity_id TEXT,
                task_name TEXT,
                route_name TEXT,
                option_code TEXT,
                lot_id TEXT,
                unit_id TEXT,
                work_order_number TEXT,
                raw_command_json TEXT,
                mqtt_topic TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed INTEGER DEFAULT 0
            )
        """
        )

        # Create migration log
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS migration_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT,
                old_id INTEGER,
                new_id INTEGER,
                entity_type TEXT,
                migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_unified_devices_entity_type ON unified_devices(entity_type)",
            "CREATE INDEX IF NOT EXISTS idx_unified_devices_device_id ON unified_devices(device_id)",
            "CREATE INDEX IF NOT EXISTS idx_device_results_device_id ON device_results(device_id)",
            "CREATE INDEX IF NOT EXISTS idx_device_results_timestamp ON device_results(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_vf_commands_timestamp ON vf_commands(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_vf_commands_device ON vf_commands(device_type, device_id)",
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

        conn.commit()


def migrate_bridges(db_path: str):
    """Migrate bridge devices."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get all bridges
        try:
            cursor.execute("SELECT * FROM bridges")
            bridges = cursor.fetchall()
        except sqlite3.OperationalError:
            logger.info("No bridges table found, skipping bridge migration")
            return

        for bridge in bridges:
            # Create bridge entry
            bridge_config = {"bridge_type": "mqtt", "original_bridge_id": bridge["id"]}

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    "bridge",
                    f"bridge_{bridge['id']}",
                    bridge["name"],
                    bridge["ip"],
                    bridge["port"],
                    bridge["status"],
                    json.dumps(bridge_config),
                ),
            )

            bridge_unified_id = cursor.lastrowid

            # Log migration
            cursor.execute(
                """
                INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                VALUES (?, ?, ?, ?)
            """,
                ("bridges", bridge["id"], bridge_unified_id, "bridge"),
            )

            # Migrate devices connected to this bridge
            try:
                cursor.execute(
                    "SELECT * FROM devices WHERE bridge_id = ?", (bridge["id"],)
                )
                devices = cursor.fetchall()

                for device in devices:
                    device_config = {
                        "bridge_id": bridge_unified_id,
                        "port": device["port"],
                        "tester_type": device.get("tester_type", "hioki"),
                        "device_type": device.get("device_type", "bridge"),
                        "original_device_id": device["id"],
                    }

                    entity_type = f"hioki_{device.get('device_type', 'bridge')}"

                    cursor.execute(
                        """
                        INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config, 
                                                   last_raw_data, last_update, testing)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                        (
                            entity_type,
                            device["hioki_id"],
                            device["hioki_id"],
                            bridge["ip"],
                            device["port"],
                            device.get("status", "unknown"),
                            json.dumps(device_config),
                            device.get("last_reading"),
                            device.get("last_update"),
                            device.get("testing", 0),
                        ),
                    )

                    device_unified_id = cursor.lastrowid

                    # Log migration
                    cursor.execute(
                        """
                        INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                        VALUES (?, ?, ?, ?)
                    """,
                        ("devices", device["id"], device_unified_id, entity_type),
                    )
            except sqlite3.OperationalError:
                pass

        conn.commit()
        logger.info(f"Migrated {len(bridges)} bridges and their devices")


def migrate_standalone_devices(db_path: str):
    """Migrate standalone devices (serial, w610, etc.)."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM devices WHERE bridge_id IS NULL")
            devices = cursor.fetchall()
        except sqlite3.OperationalError:
            logger.info("No devices table found, skipping device migration")
            return

        for device in devices:
            device_type = device.get("device_type", "unknown")
            entity_type = f"hioki_{device_type}"

            device_config = {
                "tester_type": device.get("tester_type", "hioki"),
                "device_type": device_type,
                "original_device_id": device["id"],
            }

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config,
                                           last_raw_data, last_update, testing)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    entity_type,
                    device["hioki_id"],
                    device["hioki_id"],
                    device.get("ip"),
                    device.get("port"),
                    device.get("status", "unknown"),
                    json.dumps(device_config),
                    device.get("last_reading"),
                    device.get("last_update"),
                    device.get("testing", 0),
                ),
            )

            unified_id = cursor.lastrowid

            cursor.execute(
                """
                INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                VALUES (?, ?, ?, ?)
            """,
                ("devices", device["id"], unified_id, entity_type),
            )

        conn.commit()
        logger.info(f"Migrated {len(devices)} standalone devices")


def migrate_cts_devices(db_path: str):
    """Migrate CTS devices."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM cts")
            cts_devices = cursor.fetchall()
        except sqlite3.OperationalError:
            logger.info("No CTS table found, skipping CTS migration")
            return

        for cts in cts_devices:
            cts_config = {
                "cts_type": cts.get("cts_type", "manifold"),
                "cts_id": cts.get("cts_id"),
                "original_cts_id": cts["id"],
            }

            # Parse last reading if available
            last_reading = None
            if cts.get("last_reading"):
                last_reading = parse_cts_data(cts["last_reading"])

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config,
                                           last_reading, last_raw_data, last_update)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    "cts",
                    cts.get("cts_id", cts["name"]),
                    cts["name"],
                    cts["ip"],
                    int(cts["port"]),
                    cts.get("status", "unknown"),
                    json.dumps(cts_config),
                    json.dumps(last_reading) if last_reading else None,
                    cts.get("last_reading"),
                    cts.get("last_update"),
                ),
            )

            unified_id = cursor.lastrowid

            cursor.execute(
                """
                INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                VALUES (?, ?, ?, ?)
            """,
                ("cts", cts["id"], unified_id, "cts"),
            )

        conn.commit()
        logger.info(f"Migrated {len(cts_devices)} CTS devices")


def migrate_welders(db_path: str):
    """Migrate ultrasonic welders."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM welders")
            welders = cursor.fetchall()
        except sqlite3.OperationalError:
            logger.info("No welders table found, skipping welder migration")
            return

        for welder in welders:
            welder_config = {
                "raspi_name": welder["raspi_name"],
                "welder_name": welder.get("welder_name"),
                "original_welder_id": welder["id"],
            }

            # Parse last reading if available
            last_reading = None
            if welder.get("last_reading"):
                try:
                    last_reading = json.loads(welder["last_reading"])
                except:
                    last_reading = {"raw": welder["last_reading"]}

            device_id = welder.get("welder_name") or welder["raspi_name"]

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config,
                                           last_reading, last_raw_data, last_update)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    "welder",
                    device_id,
                    welder.get("welder_name", welder["raspi_name"]),
                    welder["ip"],
                    welder["port"],
                    welder.get("status", "unknown"),
                    json.dumps(welder_config),
                    json.dumps(last_reading) if last_reading else None,
                    welder.get("last_reading"),
                    welder.get("last_update"),
                ),
            )

            unified_id = cursor.lastrowid

            cursor.execute(
                """
                INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                VALUES (?, ?, ?, ?)
            """,
                ("welders", welder["id"], unified_id, "welder"),
            )

        conn.commit()
        logger.info(f"Migrated {len(welders)} welders")


def migrate_custom_devices(db_path: str):
    """Migrate custom devices."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM custom_devices")
            custom_devices = cursor.fetchall()
        except sqlite3.OperationalError:
            logger.info(
                "No custom_devices table found, skipping custom device migration"
            )
            return

        for device in custom_devices:
            device_config = {
                "in_protocol": device["in_protocol"],
                "in_param": device.get("in_param"),
                "out_protocol": device["out_protocol"],
                "out_param": device.get("out_param"),
                "original_custom_id": device["id"],
            }

            cursor.execute(
                """
                INSERT INTO unified_devices (entity_type, device_id, name, ip, port, status, config)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    "custom",
                    device["name"],
                    device["name"],
                    device["ip"],
                    int(device["port"]),
                    "unknown",
                    json.dumps(device_config),
                ),
            )

            unified_id = cursor.lastrowid

            cursor.execute(
                """
                INSERT INTO migration_log (table_name, old_id, new_id, entity_type)
                VALUES (?, ?, ?, ?)
            """,
                ("custom_devices", device["id"], unified_id, "custom"),
            )

        conn.commit()
        logger.info(f"Migrated {len(custom_devices)} custom devices")


def parse_cts_data(raw_line: str) -> dict:
    """Parse CTS data similar to NodeRED logic."""
    import re

    if not raw_line or "*" in raw_line or "x01" in raw_line:
        return None

    parts = raw_line.split()
    if not parts:
        return None

    def search_string_in_array(pattern: str, str_array: list) -> int:
        """Find index of exact pattern match."""
        for j, val in enumerate(str_array):
            if re.fullmatch(pattern, val):
                return j
        return -1

    def contain_string_in_array(substr: str, str_array: list) -> int:
        """Find index of substring match."""
        for j, val in enumerate(str_array):
            if substr in val:
                return j
        return -1

    # Find indices for key markers
    msg1_ind = search_string_in_array(r"dpsig", parts) - 1
    msg2_ind = search_string_in_array(r"psig", parts) - 1
    msg3_ind = contain_string_in_array("|GVB|", parts)

    # Extract values
    pressureloss = parts[msg1_ind] if msg1_ind >= 0 and msg1_ind < len(parts) else None
    testpressure = parts[msg2_ind] if msg2_ind >= 0 and msg2_ind < len(parts) else None
    serial_info = parts[msg3_ind] if msg3_ind != -1 and msg3_ind < len(parts) else None

    if pressureloss is None and testpressure is None:
        return None

    return {
        "pressureloss": pressureloss,
        "testpressure": testpressure,
        "serial_info": serial_info,
        "raw_line": raw_line,
        "timestamp": datetime.now().isoformat(),
    }


def print_migration_summary(db_path: str):
    """Print summary of migrated data."""
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Count devices by entity type
        cursor.execute(
            """
            SELECT entity_type, COUNT(*) as count 
            FROM unified_devices 
            GROUP BY entity_type
        """
        )

        device_counts = cursor.fetchall()

        # Count VF commands
        cursor.execute("SELECT COUNT(*) as count FROM vf_commands")
        vf_count = cursor.fetchone()["count"]

        print("\n" + "=" * 50)
        print("MIGRATION SUMMARY")
        print("=" * 50)

        total_devices = 0
        for row in device_counts:
            count = row["count"]
            total_devices += count
            print(f"{row['entity_type']:15}: {count:3} devices")

        print("-" * 50)
        print(f"{'TOTAL':15}: {total_devices:3} devices")
        print(f"{'VF Commands':15}: {vf_count:3} commands")

        print("=" * 50)
        print("Migration completed successfully!")
        print("=" * 50 + "\n")


def main():
    """Main migration function."""
    logger.info("Starting HexMES database migration...")

    # Get database path
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DB_PATH = os.path.join(BASE_DIR, "dashboard.db")

    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found: {DB_PATH}")
        print("Please ensure your HexMES app has been run at least once.")
        return

    # Check if we have legacy data to migrate
    if not verify_legacy_tables_exist(DB_PATH):
        logger.warning("No legacy tables found. Creating unified structure only.")
        init_unified_db(DB_PATH)
        print("No migration needed - unified structure created.")
        return

    # Ask for confirmation
    print("\nThis will migrate your existing device data to the new unified structure.")
    print("A backup will be created automatically.")

    response = input("\nProceed with migration? (y/N): ").strip().lower()
    if response != "y":
        print("Migration cancelled.")
        return

    try:
        # Create backup
        backup_path = backup_database(DB_PATH)

        # Initialize unified structure
        init_unified_db(DB_PATH)

        # Check if migration already done
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM migration_log")
            if cursor.fetchone()[0] > 0:
                logger.info("Migration already completed")
                print_migration_summary(DB_PATH)
                return

        # Run migration
        logger.info("Starting data migration...")
        migrate_bridges(DB_PATH)
        migrate_standalone_devices(DB_PATH)
        migrate_cts_devices(DB_PATH)
        migrate_welders(DB_PATH)
        migrate_custom_devices(DB_PATH)

        # Print summary
        print_migration_summary(DB_PATH)

        # Provide next steps
        print("NEXT STEPS:")
        print("1. Update your app.py to import the new unified services")
        print("2. Restart your application")
        print("3. Test device functionality")
        print("4. Once verified, you can optionally drop the old tables")
        print("\nBackup location:", backup_path)

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"\nMigration failed: {e}")
        print("Your original data is unchanged.")
        sys.exit(1)


if __name__ == "__main__":
    main()
