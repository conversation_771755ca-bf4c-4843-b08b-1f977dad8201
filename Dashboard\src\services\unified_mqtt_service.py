import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Any

import paho.mqtt.client as mqtt
from .result_processors import ResultProcessorFactory

logger = logging.getLogger(__name__)

# MQTT Configuration
MQTT_BROKER = "vf-gateway-01"
MQTT_PORT = 1883
MQTT_USER = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"

# Global variables
mqtt_client = None
socketio = None
db_service = None


def setup_mqtt(socketio_instance=None, db_service_module=None):
    """Set up MQTT client with unified message handling."""
    global mqtt_client, socketio, db_service

    socketio = socketio_instance
    db_service = db_service_module

    logger.debug(f"Setting up MQTT with broker: {MQTT_BROKER}:{MQTT_PORT}")

    try:
        client_id = f"hexmes-unified-{int(datetime.now().timestamp())}"
        logger.debug(f"Creating MQTT client with ID: {client_id}")

        client = mqtt.Client(client_id=client_id)
        client.username_pw_set(MQTT_USER, MQTT_PASSWORD)
        client.on_connect = on_connect
        client.on_message = on_message

        logger.debug(f"Attempting to connect to MQTT broker: {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        mqtt_client = client
        logger.debug(f"MQTT client set globally: {mqtt_client is not None}")
        logger.info("Connected to MQTT broker with unified handlers")
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MQTT broker: {str(e)}")
        return None


def on_connect(client, userdata, flags, rc):
    """Handle MQTT connection."""
    logger.info(f"Connected to MQTT broker with result code {rc}")

    # Subscribe to all tool command topics
    client.subscribe("nomuda/gvl/tools/+/+/command")
    logger.info("Subscribed to: nomuda/gvl/tools/+/+/command")

    # Subscribe to all tool result topics
    client.subscribe("nomuda/gvl/tools/+/+/result/+")
    logger.info("Subscribed to: nomuda/gvl/tools/+/+/result/+")

    # Legacy subscriptions for existing systems
    client.subscribe("Proterra/Greenville/GVB/Pack/Hioki-Bridge/+/status")
    client.subscribe("nomuda/gvl/tools/GVB-Hioki-RM3545/+/result/impedance")


def on_message(client, userdata, msg):
    """Handle incoming MQTT messages."""
    topic = msg.topic
    try:
        payload = msg.payload.decode("utf-8")
    except UnicodeDecodeError:
        payload = msg.payload.decode("utf-8", errors="ignore")

    logger.info(f"Received MQTT message on topic: {topic}")
    logger.debug(f"Payload: {payload}")

    try:
        # Parse topic to determine message type and routing
        if "/command" in topic:
            handle_command_message(topic, payload)
        elif "/result/" in topic:
            handle_result_message(topic, payload)
        elif "status" in topic:
            handle_legacy_status_message(topic, payload)
        else:
            logger.warning(f"Unhandled MQTT topic pattern: {topic}")

    except Exception as e:
        logger.error(f"Error processing MQTT message on topic {topic}: {e}")


def parse_mqtt_topic(topic: str) -> Dict[str, str]:
    """Parse MQTT topic into components.

    Expected formats:
    - Commands: nomuda/gvl/tools/{TOOL-TYPE}/{TOOL-NAME}/command
    - Results: nomuda/gvl/tools/{TOOL-TYPE}/{TOOL-NAME}/result/{RESULT-TYPE}
    """
    parts = topic.split("/")

    if len(parts) < 5:
        return {}

    # Standard format: nomuda/gvl/tools/{TOOL-TYPE}/{TOOL-NAME}/...
    if parts[0] == "nomuda" and parts[1] == "gvl" and parts[2] == "tools":
        result = {"tool_type": parts[3], "tool_name": parts[4]}

        if len(parts) >= 6:
            if parts[5] == "command":
                result["message_type"] = "command"
            elif parts[5] == "result" and len(parts) >= 7:
                result["message_type"] = "result"
                result["result_type"] = parts[6]

        return result

    return {}


def handle_command_message(topic: str, payload: str):
    """Handle command messages from VF."""
    if not db_service:
        logger.error("Database service not available for command handling")
        return

    topic_info = parse_mqtt_topic(topic)
    if not topic_info:
        logger.warning(f"Could not parse command topic: {topic}")
        return

    try:
        # Parse command payload
        command_data = json.loads(payload)
        tool_type = topic_info.get("tool_type", "").lower()
        tool_name = topic_info.get("tool_name", "")

        # Map tool types to entity types
        entity_type = map_tool_type_to_entity_type(tool_type)

        logger.info(f"Processing {entity_type} command for {tool_name}")

        # Store the command in local database
        command_id = db_service.store_device_command(
            entity_type, tool_name, command_data
        )

        # Store the command in MESDB for linking to results
        mesdb_command_id = ResultProcessorFactory.store_command(
            entity_type, tool_name, command_data
        )

        # Emit to websocket clients
        if socketio:
            socketio.emit(
                "device_command_received",
                {
                    "command_id": command_id,
                    "mesdb_command_id": mesdb_command_id,
                    "entity_type": entity_type,
                    "tool_name": tool_name,
                    "command_data": command_data,
                    "timestamp": datetime.now().isoformat(),
                },
            )

        logger.info(
            f"Stored command {command_id} (MESDB: {mesdb_command_id}) for {entity_type}/{tool_name}"
        )

    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in command payload: {payload}")
    except Exception as e:
        logger.error(f"Error handling command message: {e}")


def handle_result_message(topic: str, payload: str):
    """Handle result messages from devices."""
    if not db_service:
        logger.error("Database service not available for result handling")
        return

    topic_info = parse_mqtt_topic(topic)
    if not topic_info:
        logger.warning(f"Could not parse result topic: {topic}")
        return

    tool_type = topic_info.get("tool_type", "").lower()
    tool_name = topic_info.get("tool_name", "")
    result_type = topic_info.get("result_type", "")

    # Map tool types to entity types
    entity_type = map_tool_type_to_entity_type(tool_type)

    logger.info(f"Processing {entity_type} result for {tool_name}, type: {result_type}")

    try:
        # Find the device this result belongs to
        device = find_device_for_result(entity_type, tool_name, topic_info)

        if not device:
            logger.warning(f"No device found for result topic: {topic}")
            # Still store the result but without device_id
            device_id = None
        else:
            device_id = device["id"]
            # Update device status to online
            db_service.update_device_status(device_id, "online")

        # Store the result
        result_id = db_service.store_device_result(
            device_id=device_id,
            entity_type=entity_type,
            raw_data=payload,
            result_type=result_type,
            mqtt_topic=topic,
        )

        # Try to link with a recent command if device is known
        if device_id:
            link_result_to_command(device_id, entity_type, result_id)

        # Get processed result for websocket emission
        results = db_service.get_device_results(device_id, limit=1) if device_id else []
        processed_data = results[0].get("processed_data") if results else None

        # Emit to websocket clients
        if socketio:
            socketio.emit(
                "device_result_received",
                {
                    "result_id": result_id,
                    "device_id": device_id,
                    "hioki_id": device.get("hioki_id") if device else None,
                    "entity_type": entity_type,
                    "tool_name": tool_name,
                    "result_type": result_type,
                    "raw_data": payload,
                    "processed_data": processed_data,
                    "timestamp": datetime.now().isoformat(),
                },
            )

            # Emit legacy format for backwards compatibility
            if entity_type == "hioki" and device:
                socketio.emit(
                    "device_reading_update",
                    {
                        "hioki_id": device["hioki_id"],
                        "reading": (
                            processed_data.get("impedance")
                            if processed_data
                            else payload
                        ),
                        "timestamp": datetime.now().isoformat(),
                    },
                )

        logger.info(f"Stored result {result_id} for {entity_type}/{tool_name}")

    except Exception as e:
        logger.error(f"Error handling result message: {e}")


def map_tool_type_to_entity_type(tool_type: str) -> str:
    """Map MQTT tool type to database entity type."""
    tool_type = tool_type.lower()

    if "hioki" in tool_type or "rm3545" in tool_type:
        return "hioki"
    elif "cts" in tool_type:
        return "cts"
    elif "ultrasonic" in tool_type or "welder" in tool_type or "branson" in tool_type:
        return "ultrasonic"
    elif "manifold" in tool_type:
        return "cts"
    elif "enclosure" in tool_type:
        return "cts"
    else:
        return "custom"


def find_device_for_result(
    entity_type: str, tool_name: str, topic_info: Dict
) -> Optional[Dict]:
    """Find which device a result belongs to."""
    if not db_service:
        return None

    # For Hioki devices, tool_name often contains the hioki_id
    if entity_type == "hioki":
        # Pattern: GVB-Hioki-RM3545/H001
        if "/" in tool_name:
            hioki_id = tool_name.split("/")[-1]  # Extract H001
            device = db_service.get_device_by_hioki_id(hioki_id, "hioki")
            if device:
                return device

        # Sometimes the hioki_id might be the full tool_name
        device = db_service.get_device_by_hioki_id(tool_name, "hioki")
        if device:
            return device

    # For CTS devices, look for name matches
    elif entity_type == "cts":
        devices = db_service.get_devices("cts")
        for device in devices:
            # Try exact name match first
            if device["name"].lower() == tool_name.lower():
                return device
            # Try partial match
            if (
                tool_name.lower() in device["name"].lower()
                or device["name"].lower() in tool_name.lower()
            ):
                return device

    # For ultrasonic welders
    elif entity_type == "ultrasonic":
        devices = db_service.get_devices("ultrasonic")
        for device in devices:
            if device["name"].lower() == tool_name.lower():
                return device

    logger.warning(f"Could not find device for {entity_type}/{tool_name}")
    return None


def link_result_to_command(device_id: int, entity_type: str, result_id: int):
    """Try to link a result to a recent command."""
    if not db_service:
        return

    try:
        result_time = datetime.now()
        matching_command = db_service.find_matching_command(
            device_id=device_id,
            entity_type=entity_type,
            result_timestamp=result_time,
            time_window_minutes=10,  # Look for commands within 10 minutes
        )

        if matching_command:
            db_service.link_command_to_result(matching_command["id"], result_id)
            db_service.mark_command_processed(matching_command["id"])
            logger.info(
                f"Linked result {result_id} to command {matching_command['id']}"
            )
        else:
            logger.debug(f"No matching command found for result {result_id}")

    except Exception as e:
        logger.error(f"Error linking result to command: {e}")


def handle_legacy_status_message(topic: str, payload: str):
    """Handle legacy bridge status messages."""
    logger.info(f"Legacy status message on {topic}: {payload}")
    # This can handle old bridge status messages if needed


def publish_device_result(device: Dict, result_data: Any, result_type: str = None):
    """Publish a device result to MQTT (for outgoing results)."""
    logger.debug(
        f"publish_device_result called with device: {device}, result_data: {result_data}"
    )

    if not mqtt_client:
        logger.warning("MQTT client not available for publishing")
        return False

    if not device:
        logger.warning("No device provided for MQTT publishing")
        return False

    try:
        entity_type = device.get("entity_type", "unknown")
        hioki_id = device.get("hioki_id") or device.get("name")

        logger.debug(f"Publishing for entity_type: {entity_type}, hioki_id: {hioki_id}")

        # Determine result type if not provided
        if not result_type:
            if entity_type in [
                "hioki",
                "hioki_w610",
                "hioki_bridge",
                "hioki_serial",
                "hioki_http",
            ]:
                result_type = "impedance"
            elif entity_type == "cts":
                result_type = "test"
            elif entity_type in ["ultrasonic", "welder"]:
                result_type = "weld_data"
            else:
                result_type = "data"

        # Build topic based on entity type
        if entity_type in [
            "hioki",
            "hioki_w610",
            "hioki_bridge",
            "hioki_serial",
            "hioki_http",
        ]:
            topic = f"nomuda/gvl/tools/GVB-Hioki-RM3545/{hioki_id}/result/{result_type}"
        elif entity_type == "cts":
            # Safely get config
            config = device.get("config", {})
            if isinstance(config, str):
                try:
                    config = json.loads(config)
                except (json.JSONDecodeError, TypeError):
                    config = {}

            if config.get("cts_type") == "enclosure":
                topic = f"nomuda/gvl/tools/GVB-CTS-Enclosure/{hioki_id}/result/{result_type}"
            else:
                topic = (
                    f"nomuda/gvl/tools/GVB-CTS-Manifold/{hioki_id}/result/{result_type}"
                )
        elif entity_type in ["ultrasonic", "welder"]:
            topic = f"nomuda/gvl/tools/GVB-BRANSON/{hioki_id}/result/{result_type}"
        else:
            topic = f"nomuda/gvl/tools/{entity_type}/{hioki_id}/result/{result_type}"

        # Convert result data to appropriate payload format
        if isinstance(result_data, dict):
            # For Hioki devices, extract just the impedance value
            if entity_type in [
                "hioki",
                "hioki_w610",
                "hioki_bridge",
                "hioki_serial",
                "hioki_http",
            ]:
                # Extract impedance value for MQTT payload
                impedance_value = result_data.get("impedance") or result_data.get(
                    "value"
                )
                if impedance_value is not None:
                    payload = str(impedance_value)
                else:
                    # Fallback to full JSON if no impedance found
                    payload = json.dumps(result_data)
            else:
                # For other device types, send full JSON
                payload = json.dumps(result_data)
        else:
            payload = str(result_data)

        # Publish
        logger.debug(f"Publishing to topic: {topic} with payload: {payload}")
        result = mqtt_client.publish(topic, payload, qos=1)

        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            logger.info(f"Published result to {topic}")
            return True
        else:
            logger.error(f"Failed to publish to {topic}, error code: {result.rc}")
            return False

    except Exception as e:
        logger.error(f"Error publishing device result: {e}")
        return False


def disconnect():
    """Disconnect MQTT client."""
    global mqtt_client
    if mqtt_client:
        try:
            mqtt_client.loop_stop()
            mqtt_client.disconnect()
            logger.info("Disconnected from MQTT broker")
        except Exception as e:
            logger.error(f"Error disconnecting from MQTT: {e}")
        mqtt_client = None


# =============================================================================
# Service Class for Compatibility
# =============================================================================


class UnifiedMQTTService:
    """Unified MQTT service class for compatibility."""

    @property
    def mqtt_client(self):
        """Get the current MQTT client."""
        return mqtt_client

    def publish_result(
        self, entity_type: str, device_id: str, result: Dict, device_config: Dict = None
    ):
        """Publish result to MQTT."""
        # Create a device dict for the publish function
        device = {"entity_type": entity_type, "hioki_id": device_id, "name": device_id}
        return publish_device_result(device, result)

    def disconnect(self):
        """Disconnect MQTT client."""
        return disconnect()


# Create global service instance
unified_mqtt_service = UnifiedMQTTService()
