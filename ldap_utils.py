import os
import logging
from ldap3 import Server, Connection, ALL

logger = logging.getLogger(__name__)


def _get_base_dn(domain: str) -> str:
    """Convert a domain name to a base DN string."""
    return ",".join(f"DC={part}" for part in domain.split(".")) if domain else ""


def authenticate(username: str, password: str) -> bool:
    """Authenticate the user against the configured LDAP server using a service account."""

    logger.info("LDAP authentication attempt for user '%s'", username)

    ldap_server = os.getenv("LDAP_SERVER")
    ldap_secondary = os.getenv("LDAP_SECONDARY_SERVER")
    ldap_domain = os.getenv("LDAP_DOMAIN", "")
    bind_user = os.getenv("LDAP_BIND_USER")
    bind_password = os.getenv("LDAP_BIND_PASSWORD")
    base_dn = os.getenv("LDAP_BASE_DN") or _get_base_dn(ldap_domain)

    if not ldap_server:
        logger.error("LDAP_SERVER not configured")
        return False

    servers = [ldap_server]
    if ldap_secondary:
        servers.append(ldap_secondary)

    search_filter = f"(sAMAccountName={username})"

    for host in servers:
        try:
            logger.info("Attempting connection to LDAP server %s", host)
            server = Server(host, get_info=ALL)
            conn = Connection(server, user=bind_user, password=bind_password, auto_bind=True)
            logger.info("Connected to %s, searching for user", host)
            conn.search(base_dn, search_filter, attributes=["distinguishedName"])
            logger.info("Search returned %d entries", len(conn.entries))
            if conn.entries:
                user_dn = conn.entries[0].distinguishedName.value
                logger.info("Found DN %s, verifying credentials", user_dn)
                try:
                    user_conn = Connection(server, user=user_dn, password=password, auto_bind=True)
                    user_conn.unbind()
                    conn.unbind()
                    logger.info("LDAP authentication successful for %s", username)
                    return True
                except Exception as e:
                    logger.error("Failed to bind as user %s on %s: %s", username, host, e)
            else:
                logger.warning("User %s not found on %s", username, host)
            conn.unbind()
        except Exception as e:
            logger.error("Error communicating with LDAP server %s: %s", host, e)
            continue

    logger.warning("LDAP authentication failed for user %s", username)
    return False
