document.addEventListener('DOMContentLoaded', function() {
    // Connect to Socket.IO server
    const socket = io();

    // Handle device reading updates
    socket.on('device_reading_update', function(data) {
        const { hioki_id, reading, timestamp } = data;
        
        console.log('Received device reading update:', data);
        
        // Update all elements showing readings for this device
        const readingElements = document.querySelectorAll(`[data-hioki-id="${hioki_id}"]`);
        readingElements.forEach(element => {
            // Elements may either directly represent the reading/timestamp or
            // contain children that do. Support both cases so that device cards
            // on the dashboard also update correctly.

            // Update reading
            if (element.classList.contains('device-reading') || element.hasAttribute('data-reading-cell')) {
                element.textContent = reading;
                console.log(`Updated reading to ${reading} for element:`, element);
            }
            const nestedReading = element.querySelector('[data-reading-cell]');
            if (nestedReading) {
                nestedReading.textContent = reading;
            }

            // Update timestamp
            if (element.classList.contains('device-timestamp') || element.hasAttribute('data-timestamp-cell')) {
                element.textContent = new Date(timestamp).toLocaleString();
            }
            const nestedTimestamp = element.querySelector('.device-timestamp, .last-seen-time, [data-timestamp-cell]');
            if (nestedTimestamp) {
                nestedTimestamp.textContent = new Date(timestamp).toLocaleString();
            }
        });
        
        // Also update table rows with this hioki_id
        const rows = document.querySelectorAll(`tr[data-hioki-id="${hioki_id}"]`);
        rows.forEach(row => {
            // Update reading cell
            const readingCell = row.querySelector('.device-reading, [data-reading-cell]');
            if (readingCell) {
                readingCell.textContent = reading || 'N/A';
            }

            // Update timestamp cell
            const timestampCell = row.querySelector('.device-timestamp, .last-update-time, .last-seen-time, [data-timestamp-cell]');
            if (timestampCell) {
                timestampCell.textContent = new Date(timestamp).toLocaleString();
            }
            
            // Update status badge
            const statusBadge = row.querySelector('.badge');
            if (statusBadge) {
                statusBadge.textContent = 'active';
                statusBadge.classList.remove('bg-secondary', 'bg-danger');
                statusBadge.classList.add('bg-success');
                statusBadge.className = statusBadge.className.replace(/status-\w+/, 'status-active');
            }
        });
    });

    // Handle device status updates
    socket.on('device_update', function(data) {
        const { device_id, hioki_id, reading, status, timestamp, testing } = data;
        
        console.log('Received device update:', data);
        
        // Update device card if it exists
        const deviceCard = document.getElementById(`device-${device_id}`);
        if (deviceCard) {
            // Update reading
            const readingElement = deviceCard.querySelector('.device-reading');
            if (readingElement) {
                readingElement.textContent = reading;
            }

            // Update timestamp
            const timestampElement = deviceCard.querySelector('.device-timestamp');
            if (timestampElement) {
                timestampElement.textContent = new Date(timestamp).toLocaleString();
            }

            // Update status
            const statusElement = deviceCard.querySelector('.device-status');
            if (statusElement) {
                statusElement.textContent = status;
                // Update status classes
                statusElement.className = `device-status status-${status}`;
            }

            // Update testing indicator
            if (testing) {
                deviceCard.classList.add('testing');
            } else {
                deviceCard.classList.remove('testing');
            }
        }
        
        // Also update table rows with this device_id or hioki_id
        const deviceRows = document.querySelectorAll(`tr[data-device-id="${device_id}"], tr[data-hioki-id="${hioki_id}"]`);
        deviceRows.forEach(row => {
            // Update reading cell
            const readingCell = row.querySelector('.device-reading, [data-reading-cell]');
            if (readingCell && reading) {
                readingCell.textContent = reading;
            }

            // Update timestamp cell
            const timestampCell = row.querySelector('.device-timestamp, .last-update-time, .last-seen-time, [data-timestamp-cell]');
            if (timestampCell && timestamp) {
                timestampCell.textContent = new Date(timestamp).toLocaleString();
            }
            
            // Update status badge
            const statusBadge = row.querySelector('.badge');
            if (statusBadge && status) {
                statusBadge.textContent = status;
                
                if (status === 'active' || status === 'online') {
                    statusBadge.classList.remove('bg-secondary', 'bg-danger');
                    statusBadge.classList.add('bg-success');
                } else {
                    statusBadge.classList.remove('bg-success');
                    statusBadge.classList.add('bg-secondary');
                }
                
                statusBadge.className = statusBadge.className.replace(/status-\w+/, `status-${status}`);
            }
            
            // Update testing status
            if (testing) {
                row.classList.add('table-warning');
            } else {
                row.classList.remove('table-warning');
            }
            
            // Update test button if it exists
            const testButton = row.querySelector('.test-reading-btn');
            if (testButton) {
                testButton.innerHTML = testing ? 
                    '<i class="bi bi-lightning"></i> Stop Test' : 
                    '<i class="bi bi-lightning"></i> Test Reading';
                testButton.setAttribute('data-testing', testing ? 'true' : 'false');
            }
        });
    });
    
    // Handle bridge status updates
    socket.on('bridge_status_update', function(data) {
        const { bridge_id, status } = data;
        
        console.log('Received bridge status update:', data);
        
        // Update bridge card if it exists
        const bridgeCard = document.querySelector(`.bridge-card[data-bridge-id="${bridge_id}"]`);
        if (bridgeCard) {
            const statusBadge = bridgeCard.querySelector('.status-badge');
            if (statusBadge) {
                statusBadge.textContent = status;
                
                if (status === 'online') {
                    statusBadge.classList.remove('bg-danger', 'bg-secondary');
                    statusBadge.classList.add('bg-success');
                } else {
                    statusBadge.classList.remove('bg-success', 'bg-secondary');
                    statusBadge.classList.add('bg-danger');
                }
            }
        }
        
        // Update bridge status in the header if on bridge detail page
        const bridgeHeader = document.querySelector(`[data-bridge-id="${bridge_id}"] .bridge-status`);
        if (bridgeHeader) {
            bridgeHeader.textContent = status;
            
            if (status === 'online') {
                bridgeHeader.classList.remove('text-danger');
                bridgeHeader.classList.add('text-success');
            } else {
                bridgeHeader.classList.remove('text-success');
                bridgeHeader.classList.add('text-danger');
            }
        }
    });

    // Add these event handlers to the existing Socket.IO setup
    socket.on('device_added_to_dashboard', function(data) {
        console.log('Device added to dashboard:', data);
        
        // If we're on the bridge detail page for this bridge, refresh the page
        const currentBridgeId = document.querySelector('[data-bridge-id]')?.getAttribute('data-bridge-id');
        if (currentBridgeId && currentBridgeId == data.bridge_id) {
            // Reload the page to show the new device
            window.location.reload();
        } else if (window.location.pathname === '/' || window.location.pathname === '/index') {
            // On the main dashboard, fetch updated device list
            fetch('/api/devices')
                .then(response => response.json())
                .then(devices => {
                    // Find the device table
                    const deviceTable = document.querySelector('table.table');
                    if (deviceTable && deviceTable.querySelector('tbody')) {
                        // Check if we need to add a new row or just refresh
                        const existingRow = document.querySelector(`tr[data-hioki-id="${data.hioki_id}"]`);
                        if (!existingRow) {
                            // If we're on the main page, just reload to show the new device
                            window.location.reload();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching updated devices:', error);
                });
        }
    });

    socket.on('device_removed_from_dashboard', function(data) {
        console.log('Device removed from dashboard:', data);
        
        // If we're on the bridge detail page for this bridge, refresh the page
        const currentBridgeId = document.querySelector('[data-bridge-id]')?.getAttribute('data-bridge-id');
        if (currentBridgeId && currentBridgeId == data.bridge_id) {
            // Reload the page to update the device list
            window.location.reload();
        } else if (window.location.pathname === '/' || window.location.pathname === '/index') {
            // On the main dashboard, remove the device row if it exists
            const deviceRow = document.querySelector(`tr[data-hioki-id="${data.hioki_id}"]`);
            if (deviceRow) {
                deviceRow.remove();
            }
        }
    });

    // Handle COM port update
    socket.on('ports_update', function(data) {
        const { bridge_id, ports } = data;
        console.log('Received ports update:', data);
        
        // Update port select dropdown if it exists
        const portSelect = document.querySelector('#portSelect');
        if (portSelect) {
            // Store currently selected value
            const currentValue = portSelect.value;
            
            // Clear existing options except the first default one
            while (portSelect.options.length > 1) {
                portSelect.remove(1);
            }
            
            // Add new port options
            ports.forEach(port => {
                const option = document.createElement('option');
                option.value = port;
                option.textContent = port;
                // If this was the previously selected port, keep it selected
                if (port === currentValue) {
                    option.selected = true;
                }
                portSelect.appendChild(option);
            });
        }

        // Update port availability indicators in device table
        const deviceTable = document.querySelector('table.devices-table');
        if (deviceTable) {
            const deviceRows = deviceTable.querySelectorAll('tr[data-port]');
            deviceRows.forEach(row => {
                const port = row.getAttribute('data-port');
                const statusIndicator = row.querySelector('.port-status');
                
                if (statusIndicator) {
                    if (ports.includes(port)) {
                        statusIndicator.textContent = 'Connected';
                        statusIndicator.classList.remove('text-danger');
                        statusIndicator.classList.add('text-success');
                    } else {
                        statusIndicator.textContent = 'Disconnected';
                        statusIndicator.classList.remove('text-success');
                        statusIndicator.classList.add('text-danger');
                    }
                }
            });
        }
    });

    // Add periodic port refresh for active bridge pages
    function setupPortRefresh() {
        const bridgeElement = document.querySelector('[data-bridge-id]');
        if (bridgeElement) {
            const bridgeId = bridgeElement.getAttribute('data-bridge-id');
            
            // Refresh ports every 5 seconds
            setInterval(() => {
                fetch(`/api/bridges/${bridgeId}/get_ports`)
                    .catch(error => console.error('Error refreshing ports:', error));
            }, 5000);
        }
    }

    // Call setup function
    setupPortRefresh();
});
