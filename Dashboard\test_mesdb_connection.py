#!/usr/bin/env python3
"""
Test script to verify MESDB connection to DEV database.
Run this script to test the connection before starting the Dashboard.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_mesdb_connection():
    """Test the MESDB connection and display results."""
    print("🔧 Testing MESDB Connection to DEV Database...")
    print("=" * 60)
    
    try:
        from services.result_processors import test_mesdb_connection, get_mesdb_status
        
        # Test basic status
        print("📊 MESDB Status:")
        status = get_mesdb_status()
        print(f"  Available: {status['available']}")
        print(f"  Enabled: {status['enabled']}")
        print(f"  Server: {status['server']}")
        print(f"  Database: {status['database']}")
        print(f"  Table: {status['table']}")
        print()
        
        # Test actual connection
        print("🔌 Testing Connection...")
        connection_test = test_mesdb_connection()
        
        if connection_test['connected']:
            print("✅ SUCCESS: Connected to MESDB!")
            print(f"  Record Count: {connection_test['record_count']}")
            print(f"  Server: {connection_test['server']}")
            print(f"  Database: {connection_test['database']}")
            print(f"  Table: {connection_test['table']}")
        else:
            print("❌ FAILED: Could not connect to MESDB")
            print(f"  Error: {connection_test['error']}")
            
        print()
        print("🧪 Testing Command Storage...")
        
        # Test storing a sample command
        from services.result_processors import store_command_in_mesdb
        
        sample_command = {
            "commandId": "test_command_001",
            "toolCommand": "TEST_IMPEDANCE",
            "context": {
                "WorkStationName": "TEST_STATION",
                "PersonName": "TEST_USER",
                "TestItemName": "MESDB_CONNECTION_TEST",
                "ActivityID": "TEST_001",
                "TaskName": "Connection Test",
                "LotID": "TEST_LOT",
                "UnitID": "TEST_UNIT",
                "WorkOrderNumber": "TEST_WO_001"
            }
        }
        
        command_id = store_command_in_mesdb("test_device", "hioki", sample_command)
        
        if command_id:
            print(f"✅ SUCCESS: Stored test command with ID: {command_id}")
        else:
            print("❌ FAILED: Could not store test command")
            
        print()
        print("🧪 Testing Result Storage...")
        
        # Test storing a sample result
        from services.result_processors import store_result_in_mesdb
        
        sample_result = {
            "impedance": 1.234,
            "unit": "ohms",
            "status": "PASS",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        result_stored = store_result_in_mesdb("test_device", "hioki", sample_result, '{"raw": "test_data"}')
        
        if result_stored:
            print("✅ SUCCESS: Stored test result")
        else:
            print("❌ FAILED: Could not store test result")
            
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        print("💡 Make sure pyodbc is installed: pip install pyodbc")
    except Exception as e:
        print(f"❌ ERROR: {e}")
        
    print()
    print("=" * 60)
    print("🏁 MESDB Connection Test Complete")

if __name__ == "__main__":
    test_mesdb_connection()
