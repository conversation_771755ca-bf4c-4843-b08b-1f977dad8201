from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    redirect,
    url_for,
    session,
    flash,
)
from flask_socketio import SocketIO
import threading
import logging
import os
import signal
import atexit
import platform
import time
import json
import requests
from functools import wraps
from dotenv import load_dotenv
from datetime import datetime, timedelta
import datetime as dt

# Ensure the project root is on the Python path so ``ldap_utils`` can be found
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

# Import LDAP authentication
try:
    from ldap_utils import authenticate
except ImportError:
    # Fallback authentication for development
    def authenticate(username, password):
        return username == "admin" and password == "admin"

    logging.warning("LDAP authentication not available, using fallback")

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Load environment variables
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ROOT_DIR = os.path.abspath(os.path.join(BASE_DIR, ".."))
load_dotenv(os.path.join(ROOT_DIR, ".env"))

# Create Flask app
app = Flask(
    __name__,
    template_folder=os.path.join(BASE_DIR, "templates"),
    static_folder=os.path.join(BASE_DIR, "static"),
)
app.config["SECRET_KEY"] = "hexmes-dashboard-secret-key"
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(hours=12)

# Create SocketIO instance
socketio = SocketIO(app, cors_allowed_origins="*", async_mode="threading")

# Environment variables
W610_SERVER_HOST = os.environ.get("W610_SERVER_HOST", "0.0.0.0")
W610_SERVER_PORT = int(os.environ.get("W610_SERVER_PORT", "15000"))
AUTH_ENABLED = os.environ.get("AUTH_ENABLED", "false").lower() in (
    "1",
    "true",
    "yes",
    "on",
)

# Global service instances - initialize them later to avoid import issues
device_manager = None
mqtt_service = None
db_service = None
stop_event = threading.Event()

# ---------------------------------------------------------------------------
# Authentication and session management
# ---------------------------------------------------------------------------


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get("username"):
            return redirect(url_for("login", next=request.url))
        return f(*args, **kwargs)

    return decorated_function


@app.before_request
def require_login():
    if request.path.startswith("/static/") or request.path.startswith("/socket.io/"):
        return
    if request.endpoint in ("login", "logout", "issue_token"):
        return

    # If authentication is disabled, allow API requests through without checks
    if not AUTH_ENABLED and request.path.startswith("/api/"):
        return

    # Allow API access via token
    token = None
    auth_header = request.headers.get("Authorization", "")
    if auth_header.startswith("Bearer "):
        token = auth_header[len("Bearer ") :]
    if not token:
        token = request.args.get("access_token")

    if token and db_service:
        username = db_service.validate_api_token(token)
        if username:
            request.api_user = username
            return

    if not session.get("username"):
        if request.path.startswith("/api/"):
            return jsonify({"error": "Unauthorized"}), 401
        return redirect(url_for("login", next=request.url))

    # Check session expiration (12 hours)
    login_time_str = session.get("login_time")
    if login_time_str:
        try:
            login_time = datetime.fromisoformat(login_time_str)
            if datetime.utcnow() - login_time > timedelta(hours=12):
                session.pop("username", None)
                session.pop("login_time", None)
                if request.path.startswith("/api/"):
                    return jsonify({"error": "Session expired"}), 401
                flash("Session expired. Please log in again.", "warning")
                return redirect(url_for("login", next=request.url))
        except Exception:
            session.pop("username", None)
            session.pop("login_time", None)
            if request.path.startswith("/api/"):
                return jsonify({"error": "Session expired"}), 401
            return redirect(url_for("login", next=request.url))


# ---------------------------------------------------------------------------
# Context processors and template helpers
# ---------------------------------------------------------------------------


@app.context_processor
def inject_globals():
    return {
        "current_year": dt.datetime.now().year,
        "version": "0.3.0-UNIFIED",
        "w610_server_host": W610_SERVER_HOST,
        "w610_server_port": W610_SERVER_PORT,
    }


# ---------------------------------------------------------------------------
# Core routes
# ---------------------------------------------------------------------------


@app.route("/login", methods=["GET", "POST"])
def login():
    next_url = request.args.get("next")
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")
        if authenticate(username, password):
            session["username"] = username
            session["login_time"] = datetime.utcnow().isoformat()
            session.permanent = False
            return redirect(next_url or url_for("index"))
        else:
            flash("Invalid credentials", "danger")
    return render_template("login.html")


@app.route("/api/token", methods=["POST"])
def issue_token():
    """Return an API token given valid credentials."""
    data = request.json or {}
    username = data.get("username")
    password = data.get("password")
    if not username or not password:
        return jsonify({"error": "Username and password required"}), 400
    if not authenticate(username, password):
        return jsonify({"error": "Invalid credentials"}), 401

    if db_service:
        token = db_service.create_api_token(username)
        return jsonify({"token": token})
    else:
        return jsonify({"error": "Service not available"}), 500


@app.route("/logout")
def logout():
    session.pop("username", None)
    session.pop("login_time", None)
    return redirect(url_for("login"))


@app.route("/")
def index():
    """Dashboard showing all device types."""
    devices = []
    bridges = []
    serial_devices = []
    cts_devices = []
    custom_devices = []
    w610_devices = []
    welders = []

    if db_service:
        try:
            all_devices = db_service.get_devices()

            # Separate devices by type for backward compatibility
            for device in all_devices:
                entity_type = device.get("entity_type", "")
                if (
                    entity_type == "hioki"
                    and device.get("connection_method") == "bridge"
                ):
                    bridges.append(device)
                elif (
                    entity_type == "hioki" and device.get("connection_method") == "w610"
                ):
                    w610_devices.append(device)
                elif (
                    entity_type == "hioki"
                    and device.get("connection_method") == "serial"
                ):
                    serial_devices.append(device)
                elif entity_type == "cts":
                    cts_devices.append(device)
                elif entity_type == "ultrasonic":
                    welders.append(device)
                elif entity_type == "custom":
                    custom_devices.append(device)
                else:
                    devices.append(device)

        except Exception as e:
            logger.error(f"Error getting devices: {e}")

    return render_template(
        "index.html",
        devices=devices,
        bridges=bridges,
        serial_devices=serial_devices,
        cts_devices=cts_devices,
        custom_devices=custom_devices,
        w610_devices=w610_devices,
        welders=welders,
    )


@app.route("/bridges")
def list_bridges():
    """List all bridges."""
    bridges = []
    if db_service:
        try:
            all_devices = db_service.get_devices()
            bridges = [d for d in all_devices if d.get("entity_type") == "bridge"]
        except Exception as e:
            logger.error(f"Error getting bridges: {e}")

    return render_template("bridges.html", bridges=bridges)


@app.route("/devices/add", methods=["GET", "POST"])
def add_device_route():
    """Unified device addition."""
    if request.method == "POST":
        if not db_service:
            if request.is_json:
                return (
                    jsonify({"status": "error", "message": "Service not available"}),
                    500,
                )
            flash("Service not available", "error")
            return redirect(url_for("add_device_route"))

        data = request.get_json() or request.form

        try:
            device_id = db_service.add_device(
                name=data.get("name"),
                entity_type=data.get("entity_type"),
                connection_method=data.get("connection_method"),
                hioki_id=data.get("hioki_id"),
                ip=data.get("ip"),
                port=data.get("port"),
                config=data.get("config", {}),
            )

            # Start the device if device_manager is available
            if device_manager:
                device = db_service.get_device_by_id(device_id)
                if device:
                    device_manager.start_device(device)

            if request.is_json:
                return jsonify({"status": "success", "device_id": device_id})
            else:
                flash("Device added successfully", "success")
                return redirect(url_for("index"))

        except Exception as e:
            logger.error(f"Error adding device: {e}")
            if request.is_json:
                return jsonify({"status": "error", "message": str(e)}), 500
            flash(f"Error adding device: {e}", "error")
            return redirect(url_for("add_device_route"))

    return render_template("add_device.html")


# Legacy route compatibility
@app.route("/add_bridge", methods=["GET", "POST"])
def add_bridge_route():
    return redirect(url_for("add_device_route"))


@app.route("/add_w610", methods=["POST"])
def add_w610_route():
    """Add W610 device."""
    if not db_service:
        flash("Service not available", "error")
        return redirect(url_for("add_device_route"))

    try:
        hioki_id = request.form.get("name")  # This is actually the hioki_id
        ip = request.form.get("ip")

        device_id = db_service.add_device(
            name=f"Hioki {hioki_id}",
            entity_type="hioki",
            connection_method="w610",
            hioki_id=hioki_id,
            ip=ip,
            port="15000",
            config={"device_type": "w610", "tester_type": "hioki"},
        )

        if device_manager:
            device = db_service.get_device_by_id(device_id)
            if device:
                device_manager.start_device(device)

        flash(f"W610 device {hioki_id} added successfully", "success")
        return redirect(url_for("index"))

    except Exception as e:
        logger.error(f"Error adding W610 device: {e}")
        flash(f"Error adding W610 device: {e}", "error")
        return redirect(url_for("add_device_route"))


@app.route("/add_cts", methods=["POST"])
def add_cts_route():
    """Add CTS device."""
    if not db_service:
        flash("Service not available", "error")
        return redirect(url_for("add_device_route"))

    try:
        name = request.form.get("name")
        ip = request.form.get("ip")
        port = request.form.get("port", "23")
        cts_id = request.form.get("cts_id")
        cts_type = request.form.get("cts_type", "manifold").lower()

        device_id = db_service.add_device(
            name=name,
            entity_type="cts",
            connection_method="telnet",
            hioki_id=cts_id,
            ip=ip,
            port=port,
            config={"cts_type": cts_type, "cts_id": cts_id},
        )

        if device_manager:
            device = db_service.get_device_by_id(device_id)
            if device:
                device_manager.start_device(device)

        flash(f"CTS device {name} added successfully", "success")
        return redirect(url_for("index"))

    except Exception as e:
        logger.error(f"Error adding CTS device: {e}")
        flash(f"Error adding CTS device: {e}", "error")
        return redirect(url_for("add_device_route"))


@app.route("/add_ultrasonic", methods=["POST"])
def add_ultrasonic_route():
    """Add ultrasonic welder."""
    if not db_service:
        flash("Service not available", "error")
        return redirect(url_for("add_device_route"))

    try:
        welder_name = request.form.get("welder_name")
        raspi_name = request.form.get("raspi_name")
        ip = request.form.get("ip")
        port = request.form.get("port", "8080")

        device_id = db_service.add_device(
            name=f"{raspi_name} ({welder_name})",
            entity_type="ultrasonic",
            connection_method="http",
            ip=ip,
            port=port,
            config={
                "raspi_name": raspi_name,
                "welder_name": welder_name,
            },
        )

        if device_manager:
            device = db_service.get_device_by_id(device_id)
            if device:
                device_manager.start_device(device)

        flash(f"Ultrasonic welder {raspi_name} added successfully", "success")
        return redirect(url_for("index"))

    except Exception as e:
        logger.error(f"Error adding ultrasonic welder: {e}")
        flash(f"Error adding ultrasonic welder: {e}", "error")
        return redirect(url_for("add_device_route"))


@app.route("/add_dickson", methods=["POST"])
def add_dickson_route():
    """Add Dickson environmental monitoring device."""
    try:
        from .services.environmental_service import environmental_service

        name = request.form.get("name")
        location = request.form.get("location")
        ip_address = request.form.get("ip_address")
        location_type = request.form.get("location_type", "factory_floor")
        temp_min = request.form.get("temp_min")
        temp_max = request.form.get("temp_max")

        # Convert temperature values to float if provided
        if temp_min:
            temp_min = float(temp_min)
        if temp_max:
            temp_max = float(temp_max)

        device_id = environmental_service.add_dickson_device(
            name=name,
            location=location,
            ip_address=ip_address,
            location_type=location_type,
            temp_min=temp_min,
            temp_max=temp_max,
        )

        flash(f"Dickson device {name} added successfully", "success")
        return redirect(url_for("index"))

    except Exception as e:
        logger.error(f"Error adding Dickson device: {e}")
        flash(f"Error adding Dickson device: {e}", "error")
        return redirect(url_for("add_device_route"))


@app.route("/add_custom", methods=["POST"])
def add_custom_route():
    """Add custom device."""
    if not db_service:
        flash("Service not available", "error")
        return redirect(url_for("add_device_route"))

    try:
        name = request.form.get("name")
        ip = request.form.get("ip")
        port = request.form.get("port", "80")
        in_protocol = request.form.get("in_protocol")
        in_param = request.form.get("in_param")
        out_protocol = request.form.get("out_protocol")
        out_param = request.form.get("out_param")

        config = {
            "in_protocol": in_protocol,
            "in_param": in_param,
            "out_protocol": out_protocol,
            "out_param": out_param,
        }

        device_id = db_service.add_device(
            name=name,
            entity_type="custom",
            connection_method="http",
            ip=ip,
            port=port,
            config=config,
        )

        flash(f"Custom device {name} added successfully", "success")
        return redirect(url_for("index"))

    except Exception as e:
        logger.error(f"Error adding custom device: {e}")
        flash(f"Error adding custom device: {e}", "error")
        return redirect(url_for("add_device_route"))


# ---------------------------------------------------------------------------
# Weather Monitor API
# ---------------------------------------------------------------------------


@app.route("/api/environmental/weather/update", methods=["POST"])
def receive_weather_update():
    """Receive weather data pushed from GatewayChecker"""
    try:
        weather_data = request.get_json()

        if not weather_data:
            return (
                jsonify({"status": "error", "message": "No weather data provided"}),
                400,
            )

        # Validate required fields
        required_fields = ["temperature_c", "temperature_f", "humidity", "timestamp"]
        missing_fields = [
            field for field in required_fields if field not in weather_data
        ]

        if missing_fields:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Missing fields: {', '.join(missing_fields)}",
                    }
                ),
                400,
            )

        try:
            # Import your unified database service
            from .services.unified_db_service import unified_service

            # Store weather data as a device result
            # We'll use device_id=None and entity_type='weather' to indicate this is weather data
            result_id = unified_service.store_device_result(
                device_identifier="greer_weather_station",
                entity_type="weather",
                raw_data=json.dumps(weather_data),
                parsed_data={
                    "temperature_c": weather_data["temperature_c"],
                    "temperature_f": weather_data["temperature_f"],
                    "humidity": weather_data["humidity"],
                    "location": weather_data.get("location", "Greer, SC"),
                    "description": weather_data.get("description", ""),
                    "timestamp": weather_data["timestamp"],
                },
            )

            logger.info(
                f"Weather data stored with result_id {result_id}: {weather_data['temperature_c']}°C, {weather_data['humidity']}%"
            )

            return (
                jsonify(
                    {
                        "status": "success",
                        "message": "Weather data received and stored",
                        "result_id": result_id,
                        "timestamp": datetime.now().isoformat(),
                    }
                ),
                200,
            )

        except Exception as e:
            logger.error(f"Error storing weather data: {e}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Failed to store weather data: {str(e)}",
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"Error processing weather update: {e}")
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"Error processing weather data: {str(e)}",
                }
            ),
            500,
        )


@app.route("/api/environmental/weather", methods=["GET"])
def get_current_weather():
    """Get current weather data for the frontend"""
    try:
        # Get the latest weather reading from the database
        from .services.unified_db_service import unified_service

        # Get the most recent weather reading
        weather_reading = unified_service.get_last_reading(
            "weather", "greer_weather_station"
        )

        if weather_reading:
            return jsonify({"status": "success", "data": weather_reading})
        else:
            # Fallback: try to fetch from GatewayChecker proxy
            import requests

            try:
                response = requests.get(
                    "http://*************:5000/api/weather", timeout=10
                )
                response.raise_for_status()
                proxy_data = response.json()

                if not proxy_data.get("error"):
                    return jsonify({"status": "success", "data": proxy_data})
                else:
                    return (
                        jsonify(
                            {
                                "status": "error",
                                "message": proxy_data.get(
                                    "message", "Weather service error"
                                ),
                            }
                        ),
                        503,
                    )

            except Exception as e:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": f"No weather data available: {str(e)}",
                        }
                    ),
                    503,
                )

    except Exception as e:
        logger.error(f"Error getting weather data: {e}")
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"Error retrieving weather data: {str(e)}",
                }
            ),
            500,
        )


def fetch_weather_from_proxy():
    """Fetch weather data from GatewayChecker proxy and store it locally"""
    try:
        # Get weather data from GatewayChecker
        response = requests.get("http://*************:5000/api/weather", timeout=10)
        response.raise_for_status()

        weather_data = response.json()

        if not weather_data.get("error"):
            # Post the data to our own weather update endpoint
            update_response = requests.post(
                "http://localhost:8000/api/environmental/weather/update",
                json=weather_data,
                headers={"Content-Type": "application/json"},
                timeout=5,
            )

            if update_response.status_code == 200:
                print(
                    f"Weather data updated: {weather_data['temperature_c']}°C, {weather_data['humidity']}%"
                )
            else:
                print(
                    f"Failed to store weather data locally: {update_response.status_code}"
                )
        else:
            print(f"Weather proxy returned error: {weather_data.get('message')}")

    except requests.exceptions.ConnectionError:
        print(
            "Cannot connect to GatewayChecker weather proxy - check if service is running"
        )
    except Exception as e:
        print(f"Error fetching weather data: {e}")


def weather_background_service():
    """Background service to fetch weather data every 10 seconds"""
    while True:
        try:
            fetch_weather_from_proxy()
        except Exception as e:
            print(f"Weather background service error: {e}")

        time.sleep(10)  # Wait 10 seconds


def start_weather_service():
    """Start the weather background service"""
    weather_thread = threading.Thread(target=weather_background_service, daemon=True)
    weather_thread.start()
    print("Weather background service started")


# ---------------------------------------------------------------------------
# Database Settings Routes
# ---------------------------------------------------------------------------


@app.route("/api/mesdb/status")
@login_required
def mesdb_status():
    """Get MESDB connection status."""
    try:
        from .services.result_processors import get_mesdb_status

        status = get_mesdb_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting MESDB status: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/mesdb/test")
@login_required
def mesdb_test():
    """Test MESDB connection."""
    try:
        from .services.result_processors import test_mesdb_connection

        result = test_mesdb_connection()
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error testing MESDB connection: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/mesdb/enable", methods=["POST"])
@login_required
def mesdb_enable():
    """Enable or disable MESDB connection."""
    try:
        from .services.result_processors import set_mesdb_enabled

        data = request.get_json()
        enabled = data.get("enabled", True)

        result = set_mesdb_enabled(enabled)
        return jsonify(
            {
                "enabled": result,
                "message": f"MESDB {'enabled' if result else 'disabled'}",
            }
        )
    except Exception as e:
        logger.error(f"Error setting MESDB enabled state: {e}")
        return jsonify({"error": str(e)}), 500


# ---------------------------------------------------------------------------
# Socket.IO handlers
# ---------------------------------------------------------------------------


@socketio.on("connect")
def handle_connect():
    logger.info(f"Client connected: {request.sid}")


@socketio.on("disconnect")
def handle_disconnect():
    logger.info(f"Client disconnected: {request.sid}")


# ---------------------------------------------------------------------------
# Service initialization
# ---------------------------------------------------------------------------


def init_services():
    """Initialize all services."""
    global device_manager, mqtt_service, db_service

    try:
        # Initialize database service
        from .services.unified_db_service import init_db

        init_db()
        logger.info("Initialized unified database")

        # Import db_service module
        from .services import unified_db_service as db_service_module

        db_service = db_service_module
        logger.info("Database service ready")

        # Initialize MQTT service
        from .services.unified_mqtt_service import setup_mqtt

        mqtt_service = setup_mqtt(socketio, db_service_module)
        logger.info("Initialized MQTT service")

        # Initialize device manager
        from .services.unified_device_manager import UnifiedDeviceManager

        device_manager = UnifiedDeviceManager(db_service_module, mqtt_service, socketio)
        logger.info("Initialized device manager")

        # Register API routes
        from .services.unified_api_routes import register_unified_api_routes

        register_unified_api_routes(
            app, db_service_module, device_manager, mqtt_service
        )
        logger.info("Registered unified API routes")

    except Exception as e:
        logger.error(f"Error initializing services: {e}")
        # Don't raise the error, allow the app to start even if services fail
        logger.error(
            "Some services failed to initialize, continuing with limited functionality"
        )


def start_background_tasks():
    """Start unified device management."""
    global device_manager
    if device_manager:
        try:
            device_manager.start_all_devices()
            logger.info("Started unified device management system")
            start_weather_service()
            logger.info("Started weather background service")
        except Exception as e:
            logger.error(f"Error starting background tasks: {e}")


# ---------------------------------------------------------------------------
# Utility functions for run scripts
# ---------------------------------------------------------------------------


def get_local_ip():
    """Get the local IP address."""
    import socket

    try:
        # Connect to a remote address to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"


# ---------------------------------------------------------------------------
# Shutdown handling
# ---------------------------------------------------------------------------


def shutdown_handler(signum=None, frame=None):
    """Gracefully stop all services."""
    global device_manager, mqtt_service, stop_event

    if stop_event.is_set():
        return

    logger.info(
        f"Received shutdown signal {signum if signum else 'atexit'}, shutting down gracefully..."
    )
    stop_event.set()

    # Stop device manager
    if device_manager:
        try:
            logger.info("Stopping device manager...")
            device_manager.stop_all_devices()
            logger.info("Device manager stopped")
        except Exception as e:
            logger.error(f"Error stopping device manager: {e}")

    # Stop MQTT
    if mqtt_service and hasattr(mqtt_service, "disconnect"):
        try:
            logger.info("Stopping MQTT service...")
            mqtt_service.disconnect()
            logger.info("MQTT service stopped")
        except Exception as e:
            logger.error(f"Error stopping MQTT service: {e}")

    logger.info("Shutdown complete")

    # Force exit to ensure the application actually stops
    import os
    import time

    time.sleep(1.0)  # Give more time for cleanup on Windows
    logger.info("Forcing exit...")
    os._exit(0)


# Register graceful shutdown handlers
atexit.register(shutdown_handler)


# Enhanced Windows signal handling
def setup_signal_handlers():
    """Set up signal handlers with Windows compatibility."""

    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        shutdown_handler(signum, frame)

    # Handle SIGINT (Ctrl+C) on all platforms
    signal.signal(signal.SIGINT, signal_handler)
    logger.info("Registered SIGINT handler (Ctrl+C)")

    # Handle SIGTERM on Unix-like systems
    if hasattr(signal, "SIGTERM"):
        signal.signal(signal.SIGTERM, signal_handler)
        logger.info("Registered SIGTERM handler")

    # Windows-specific signal handling
    if platform.system() == "Windows":
        # Handle SIGBREAK (Ctrl+Break on Windows)
        if hasattr(signal, "SIGBREAK"):
            signal.signal(signal.SIGBREAK, signal_handler)
            logger.info("Registered SIGBREAK handler (Ctrl+Break)")

        # Set console control handler for Windows
        try:
            import win32api

            def console_ctrl_handler(ctrl_type):
                logger.info(f"Windows console control event: {ctrl_type}")
                if ctrl_type in (0, 2):  # CTRL_C_EVENT or CTRL_CLOSE_EVENT
                    shutdown_handler()
                    return True
                return False

            win32api.SetConsoleCtrlHandler(console_ctrl_handler, True)
            logger.info("Registered Windows console control handler")
        except ImportError:
            logger.warning(
                "pywin32 not available - using alternative Windows signal handling"
            )

            # Alternative approach: Override the default SIGINT behavior
            def enhanced_sigint_handler(signum, frame):
                logger.info("Enhanced SIGINT handler triggered on Windows")
                shutdown_handler(signum, frame)

            signal.signal(signal.SIGINT, enhanced_sigint_handler)

            # Also try to handle console close events with a different approach
            try:
                import ctypes
                from ctypes import wintypes

                kernel32 = ctypes.windll.kernel32

                def console_handler(ctrl_type):
                    logger.info(f"Windows console event: {ctrl_type}")
                    if ctrl_type in (0, 2, 5, 6):  # Various close events
                        shutdown_handler()
                        return True
                    return False

                # Define the handler type
                HANDLER_ROUTINE = ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.DWORD)
                handler = HANDLER_ROUTINE(console_handler)

                # Set the console control handler
                if kernel32.SetConsoleCtrlHandler(handler, True):
                    logger.info("Registered Windows console control handler via ctypes")
                else:
                    logger.warning("Failed to register Windows console control handler")

            except Exception as e:
                logger.warning(f"Could not set up Windows console handler: {e}")
                logger.info("Using basic SIGINT handling only")


# Set up signal handlers
setup_signal_handlers()


# ---------------------------------------------------------------------------
# Initialize services when module is loaded
# ---------------------------------------------------------------------------

# Initialize services
init_services()

# Start background tasks on first request
background_tasks_started = False


@app.before_request
def before_request_func():
    global background_tasks_started
    if not background_tasks_started:
        try:
            start_background_tasks()
            background_tasks_started = True
        except Exception as e:
            logger.error(f"Error starting background tasks: {e}")


# ---------------------------------------------------------------------------
# Application factory
# ---------------------------------------------------------------------------


def create_app():
    """Application factory function."""
    return app


if __name__ == "__main__":
    socketio.run(app, host="0.0.0.0", port=8000, debug=False)
