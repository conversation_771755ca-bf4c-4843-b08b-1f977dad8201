-- MESDB Table Creation Script
-- Run this script on your MESDB SQL Server to create the required table

USE REVMES;
GO

-- Create the main ToolData table for storing commands and results
CREATE TABLE mes.VF_ToolData (
    VFToolID BIGINT IDENTITY(1,1) PRIMARY KEY,
    
    -- Device/Tool Information
    DeviceID NVARCHAR(50) NOT NULL,           -- hioki_id, cts_id, welder_id
    DeviceType NVARCHAR(20) NOT NULL,         -- 'hioki', 'cts', 'ultrasonic'
    DeviceName NVARCHAR(100),                 -- Friendly device name
    
    -- Record Type and Linkage
    RecordType NVARCHAR(10) NOT NULL,         -- 'COMMAND' or 'RESULT'
    CommandID NVARCHAR(100),                  -- Unique command identifier from VF
    LinkedCommandID NVARCHAR(100),            -- For results: references the command they're linked to
    
    -- Timestamps
    ReceivedTimestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedTimestamp DATETIME2,
    
    -- Command Context (from VF)
    WorkStationName NVARCHAR(100),
    PersonName NVARCHAR(100),
    TestItemName NVARCHAR(200),
    ActivityID NVARCHAR(50),
    TaskName NVARCHAR(200),
    RouteName NVARCHAR(200),
    OptionCode NVARCHAR(100),
    LotID NVARCHAR(50),
    UnitID NVARCHAR(50),
    WorkOrderNumber NVARCHAR(100),
    
    -- Tool Command Information
    ToolCommand NVARCHAR(200),               -- The actual command sent
    ToolCommandData NVARCHAR(MAX),           -- JSON of full command data
    
    -- Result Data (for RESULT records)
    ResultData NVARCHAR(MAX),                -- JSON of processed result
    ResultValue NVARCHAR(100),               -- Primary result value (impedance, pressure, etc.)
    ResultUnit NVARCHAR(20),                 -- Unit of measurement
    ResultStatus NVARCHAR(20),               -- 'PASS', 'FAIL', 'ERROR', etc.
    
    -- Raw Data
    RawData NVARCHAR(MAX),                   -- Original raw data received
    
    -- Additional Metadata
    IPAddress NVARCHAR(45),                  -- Source IP address
    ProcessorVersion NVARCHAR(20),           -- Version of result processor used
    
    -- Constraints
    CONSTRAINT CK_ToolData_RecordType CHECK (RecordType IN ('COMMAND', 'RESULT')),
    CONSTRAINT CK_ToolData_DeviceType CHECK (DeviceType IN ('hioki', 'cts', 'ultrasonic', 'custom'))
);
GO

-- Create indexes for performance
CREATE INDEX IX_VF_ToolData_DeviceID_ReceivedTimestamp ON mes.VF_ToolData (DeviceID, ReceivedTimestamp);
CREATE INDEX IX_VF_ToolData_CommandID ON mes.VF_ToolData (CommandID);
CREATE INDEX IX_VF_ToolData_LinkedCommandID ON mes.VF_ToolData (LinkedCommandID);
CREATE INDEX IX_VF_ToolData_RecordType_DeviceID ON mes.VF_ToolData (RecordType, DeviceID);
CREATE INDEX IX_VF_ToolData_WorkOrderNumber ON mes.VF_ToolData (WorkOrderNumber);
CREATE INDEX IX_VF_ToolData_UnitID ON mes.VF_ToolData (UnitID);
CREATE INDEX IX_VF_ToolData_ReceivedTimestamp ON mes.VF_ToolData (ReceivedTimestamp);
GO

-- Create a view for easy command-result linking
CREATE VIEW mes.VF_ToolCommandResultView AS
SELECT
    c.VFToolID as CommandID,
    c.DeviceID,
    c.DeviceType,
    c.CommandID as CommandIdentifier,
    c.ToolCommand,
    c.WorkStationName,
    c.PersonName,
    c.TestItemName,
    c.ActivityID,
    c.TaskName,
    c.OptionCode,
    c.LotID,
    c.UnitID,
    c.WorkOrderNumber,
    c.ReceivedTimestamp as CommandTimestamp,
    r.VFToolID as ResultID,
    r.ResultValue,
    r.ResultUnit,
    r.ResultStatus,
    r.ReceivedTimestamp as ResultTimestamp,
    DATEDIFF(SECOND, c.ReceivedTimestamp, r.ReceivedTimestamp) as TimeBetweenCommandAndResult
FROM mes.VF_ToolData c
LEFT JOIN mes.VF_ToolData r ON c.CommandID = r.LinkedCommandID
WHERE c.RecordType = 'COMMAND'
    AND (r.RecordType = 'RESULT' OR r.RecordType IS NULL);
GO



PRINT 'MESDB ToolData table and indexes created successfully';
