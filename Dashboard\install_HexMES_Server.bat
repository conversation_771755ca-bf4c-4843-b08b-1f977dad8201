@echo off
setlocal enabledelayedexpansion
cd /d "%~dp0"

:: Enable ANSI color codes
for /f "tokens=3" %%a in ('reg query "HKCU\Console" /v "VirtualTerminalLevel" 2^>nul') do set VT_ENABLED=%%a
if not "!VT_ENABLED!"=="0x1" (
    reg add "HKCU\Console" /v "VirtualTerminalLevel" /t REG_DWORD /d 1 /f >nul 2>&1
)

:: Color codes
set GREEN=[92m
set GRAY=[90m
set CYAN=[96m
set YELLOW=[93m
set RED=[91m
set RESET=[0m
set BRIGHT_GREEN=[32;1m
set BRIGHT_GRAY=[37;1m

:MAIN_MENU
cls
echo.
echo %GREEN%██╗  ██╗███████╗██╗  ██╗%GRAY%███╗   ███╗███████╗███████╗%RESET%
echo %GREEN%██║  ██║██╔════╝╚██╗██╔╝%GRAY%████╗ ████║██╔════╝██╔════╝%RESET%
echo %GREEN%███████║█████╗   ╚███╔╝ %GRAY%██╔████╔██║█████╗  ███████╗%RESET%
echo %GREEN%██╔══██║██╔══╝   ██╔██╗ %GRAY%██║╚██╔╝██║██╔══╝  ╚════██║%RESET%
echo %GREEN%██║  ██║███████╗██╔╝ ██╗%GRAY%██║ ╚═╝ ██║███████╗███████║%RESET%
echo %GREEN%╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝%GRAY%╚═╝     ╚═╝╚══════╝╚══════╝%RESET%
echo.
echo %CYAN%        Wireless Admin Dashboard Installer%RESET%
echo %GRAY%        Version 0.2.6 - Proterra Manufacturing%RESET%
echo.
echo %YELLOW%══════════════════════════════════════════════════════════%RESET%
echo.
echo   %BRIGHT_GREEN%1.%RESET% Check System Requirements
echo   %BRIGHT_GREEN%2.%RESET% Install Python ^& Dependencies
echo   %BRIGHT_GREEN%3.%RESET% Configure Environment
echo   %BRIGHT_GREEN%4.%RESET% Install as Windows Service
echo   %BRIGHT_GREEN%5.%RESET% Start/Stop Service
echo   %BRIGHT_GREEN%6.%RESET% View Service Status
echo   %BRIGHT_GREEN%7.%RESET% Configure IIS Reverse Proxy
echo   %BRIGHT_GREEN%8.%RESET% Uninstall Service
echo   %BRIGHT_GREEN%9.%RESET% Exit
echo.
echo %YELLOW%══════════════════════════════════════════════════════════%RESET%
echo.
set /p choice=%CYAN%Please select an option (1-9): %RESET%

if "%choice%"=="1" goto CHECK_REQUIREMENTS
if "%choice%"=="2" goto INSTALL_PYTHON
if "%choice%"=="3" goto CONFIGURE_ENV
if "%choice%"=="4" goto INSTALL_SERVICE
if "%choice%"=="5" goto MANAGE_SERVICE
if "%choice%"=="6" goto SERVICE_STATUS
if "%choice%"=="7" goto CONFIGURE_PROXY
if "%choice%"=="8" goto UNINSTALL_SERVICE
if "%choice%"=="9" goto EXIT
goto MAIN_MENU

:CHECK_REQUIREMENTS
cls
echo %CYAN%Checking System Requirements...%RESET%
echo.

:: Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo %RED%ERROR: This installer must be run as Administrator%RESET%
    echo %YELLOW%Please right-click and select "Run as administrator"%RESET%
    pause
    goto MAIN_MENU
)

:: Check Python version
echo %YELLOW%Checking Python installation...%RESET%
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%Python is not installed or not in PATH%RESET%
    set PYTHON_MISSING=1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo %GREEN%Found Python !PYTHON_VERSION!%RESET%
    
    :: Check if Python version is 3.11 or later
    for /f "tokens=1,2 delims=." %%a in ("!PYTHON_VERSION!") do (
        set MAJOR=%%a
        set MINOR=%%b
    )
    if !MAJOR! lss 3 (
        echo %RED%Python !PYTHON_VERSION! is too old. Python 3.11+ required.%RESET%
        set PYTHON_OLD=1
    ) else if !MAJOR! equ 3 if !MINOR! lss 11 (
        echo %RED%Python !PYTHON_VERSION! is too old. Python 3.11+ required.%RESET%
        set PYTHON_OLD=1
    ) else (
        echo %GREEN%Python version is compatible%RESET%
    )
)

:: Check pip
echo %YELLOW%Checking pip...%RESET%
pip --version >nul 2>&1
if errorlevel 1 (
    echo %RED%pip is not available%RESET%
) else (
    echo %GREEN%pip is available%RESET%
)

:: Check NSSM
echo %YELLOW%Checking NSSM (service manager)...%RESET%
if exist "%~dp0nssm.exe" (
    echo %GREEN%NSSM found in current directory%RESET%
) else (
    nssm version >nul 2>&1
    if errorlevel 1 (
        echo %YELLOW%NSSM not found. Will download during installation.%RESET%
    ) else (
        echo %GREEN%NSSM is available in system PATH%RESET%
    )
)

:: Check IIS
echo %YELLOW%Checking IIS...%RESET%
sc query W3SVC >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%IIS is not installed or not running%RESET%
    echo %GRAY%  Note: IIS is optional for reverse proxy setup%RESET%
) else (
    echo %GREEN%IIS Web Server is available%RESET%
)

echo.
echo %CYAN%System check complete.%RESET%
pause
goto MAIN_MENU

:INSTALL_PYTHON
cls
echo %CYAN%Installing Python and Dependencies...%RESET%
echo.

:: Check if Python is already suitable
python --version >nul 2>&1
if not errorlevel 1 (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    for /f "tokens=1,2 delims=." %%a in ("!PYTHON_VERSION!") do (
        set MAJOR=%%a
        set MINOR=%%b
    )
    if !MAJOR! gtr 3 goto INSTALL_DEPS
    if !MAJOR! equ 3 if !MINOR! geq 11 goto INSTALL_DEPS
)

echo %YELLOW%Downloading Python 3.11...%RESET%
:: Download Python installer
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python-installer.exe'}"

if exist python-installer.exe (
    echo %CYAN%Installing Python 3.11...%RESET%
    python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
    if errorlevel 1 (
        echo %RED%Python installation failed%RESET%
        pause
        goto MAIN_MENU
    )
    echo %GREEN%Python installed successfully%RESET%
    del python-installer.exe
) else (
    echo %RED%Failed to download Python installer%RESET%
    pause
    goto MAIN_MENU
)

:INSTALL_DEPS
echo %YELLOW%Installing HexMES dependencies...%RESET%
python -m pip install --upgrade pip

if exist requirements.txt (
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo %RED%Failed to install some dependencies%RESET%
        pause
        goto MAIN_MENU
    )
    echo %GREEN%Dependencies installed successfully%RESET%
) else (
    echo %RED%requirements.txt not found in current directory%RESET%
    pause
    goto MAIN_MENU
)

echo %GREEN%Python and dependencies installation complete!%RESET%
pause
goto MAIN_MENU

:CONFIGURE_ENV
cls
echo %CYAN%Configuring Environment...%RESET%
echo.

:: Create .env file if it doesn't exist
if not exist .env (
    echo %YELLOW%Creating .env configuration file...%RESET%
    (
        echo # HexMES Dashboard Configuration
        echo AUTH_ENABLED=false
        echo W610_SERVER_HOST=0.0.0.0
        echo W610_SERVER_PORT=15000
        echo MQTT_BROKER=vf-gateway-01
        echo MQTT_PORT=1883
        echo MQTT_USER=visualfactory
        echo MQTT_PASSWORD=Pr0terr@
    ) > .env
    echo %GREEN%.env file created%RESET%
) else (
    echo %GREEN%.env file already exists%RESET%
)

:: Create logs directory
if not exist logs mkdir logs
echo %GREEN%Logs directory ready%RESET%

:: Test the application
echo %YELLOW%Testing application startup...%RESET%
timeout /t 2 >nul
python -c "from src.app import create_app; app = create_app(); print('✓ Application imports successfully')" 2>nul
if errorlevel 1 (
    echo %RED%Application test failed%RESET%
    echo %YELLOW%Please check your Python installation and dependencies%RESET%
) else (
    echo %GREEN%Application test passed%RESET%
)

echo.
echo %GREEN%Environment configuration complete!%RESET%
pause
goto MAIN_MENU

:INSTALL_SERVICE
cls
echo %CYAN%Installing HexMES as Windows Service...%RESET%
echo.

:: Download NSSM if not present
if not exist nssm.exe (
    if not exist nssm (
        echo %YELLOW%Downloading NSSM...%RESET%
        powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nssm.cc/release/nssm-2.24.zip' -OutFile 'nssm.zip'}"
        if exist nssm.zip (
            powershell -Command "Expand-Archive -Path 'nssm.zip' -DestinationPath '.' -Force"
            copy "nssm-2.24\win64\nssm.exe" "nssm.exe" >nul
            rmdir /s /q nssm-2.24 2>nul
            del nssm.zip 2>nul
            echo %GREEN%NSSM downloaded%RESET%
        ) else (
            echo %RED%Failed to download NSSM%RESET%
            pause
            goto MAIN_MENU
        )
    )
)

:: Stop service if already running
echo %YELLOW%Checking for existing service...%RESET%
sc query "HexMES Dashboard" >nul 2>&1
if not errorlevel 1 (
    echo %YELLOW%Stopping existing service...%RESET%
    nssm stop "HexMES Dashboard"
    timeout /t 3 >nul
)

:: Install or reinstall service
echo %YELLOW%Installing service...%RESET%
set "PYTHON_PATH="
for /f "delims=" %%i in ('where python 2^>nul') do set "PYTHON_PATH=%%i"
set "SCRIPT_PATH=%~dp0run_prod.py"
set "WORKING_DIR=%~dp0"

nssm install "HexMES Dashboard" "!PYTHON_PATH!" "!SCRIPT_PATH!"
nssm set "HexMES Dashboard" AppDirectory "!WORKING_DIR!"
nssm set "HexMES Dashboard" DisplayName "HexMES Wireless Admin Dashboard"
nssm set "HexMES Dashboard" Description "Proterra HexMES Wireless Device Administration Dashboard"
nssm set "HexMES Dashboard" Start SERVICE_AUTO_START
nssm set "HexMES Dashboard" AppRestartDelay 5000
nssm set "HexMES Dashboard" AppStdout "!WORKING_DIR!logs\service.log"
nssm set "HexMES Dashboard" AppStderr "!WORKING_DIR!logs\service-error.log"
nssm set "HexMES Dashboard" AppRotateFiles 1
nssm set "HexMES Dashboard" AppRotateOnline 1
nssm set "HexMES Dashboard" AppRotateSeconds 86400
nssm set "HexMES Dashboard" AppRotateBytes 10485760

if errorlevel 1 (
    echo %RED%Service installation failed%RESET%
    pause
    goto MAIN_MENU
)

echo %GREEN%Service installed successfully!%RESET%
echo.
echo %YELLOW%Starting service...%RESET%
nssm start "HexMES Dashboard"
timeout /t 3 >nul

sc query "HexMES Dashboard" | find "RUNNING" >nul
if not errorlevel 1 (
    echo %GREEN%Service is running!%RESET%
    echo.
    echo %CYAN%Dashboard should be available at:%RESET%
    for /f "tokens=2" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
        for /f "tokens=2 delims=:" %%j in ("%%i") do echo %BRIGHT_GREEN%  http://%%j:8000%RESET%
    )
) else (
    echo %RED%Service failed to start. Check logs in logs/ directory%RESET%
)

pause
goto MAIN_MENU

:MANAGE_SERVICE
cls
echo %CYAN%Manage HexMES Service%RESET%
echo.
echo %BRIGHT_GREEN%1.%RESET% Start Service
echo %BRIGHT_GREEN%2.%RESET% Stop Service  
echo %BRIGHT_GREEN%3.%RESET% Restart Service
echo %BRIGHT_GREEN%4.%RESET% Back to Main Menu
echo.
set /p svc_choice=%CYAN%Select option: %RESET%

if "%svc_choice%"=="1" (
    echo %YELLOW%Starting service...%RESET%
    nssm start "HexMES Dashboard"
    timeout /t 2 >nul
)
if "%svc_choice%"=="2" (
    echo %YELLOW%Stopping service...%RESET%
    nssm stop "HexMES Dashboard"
    timeout /t 2 >nul
)
if "%svc_choice%"=="3" (
    echo %YELLOW%Restarting service...%RESET%
    nssm restart "HexMES Dashboard"
    timeout /t 3 >nul
)
if "%svc_choice%"=="4" goto MAIN_MENU

echo %GREEN%Operation completed%RESET%
pause
goto MAIN_MENU

:SERVICE_STATUS
cls
echo %CYAN%HexMES Dashboard Service Status%RESET%
echo.
sc query "HexMES Dashboard" 2>nul
if errorlevel 1 (
    echo %RED%Service is not installed%RESET%
) else (
    echo.
    echo %YELLOW%Recent log entries:%RESET%
    echo %GRAY%----------------------------------------%RESET%
    if exist "logs\service.log" (
        powershell -Command "Get-Content 'logs\service.log' | Select-Object -Last 10"
    ) else (
        echo %YELLOW%No log file found%RESET%
    )
)
echo.
pause
goto MAIN_MENU

:CONFIGURE_PROXY
cls
echo %CYAN%Configure IIS Reverse Proxy%RESET%
echo.
echo %YELLOW%This will create a reverse proxy configuration for easier URL access%RESET%
echo %GRAY%Example: https://gr-ot-igngw-02p.mes.proterra.com/hexmes%RESET%
echo.

:: Check if IIS and URL Rewrite are available
sc query W3SVC >nul 2>&1
if errorlevel 1 (
    echo %RED%IIS is not running. Please install and start IIS first.%RESET%
    pause
    goto MAIN_MENU
)

echo %YELLOW%Creating web.config for IIS reverse proxy...%RESET%

:: Create hexmes directory in wwwroot
if not exist "C:\inetpub\wwwroot\hexmes" mkdir "C:\inetpub\wwwroot\hexmes"

:: Create web.config
(
echo ^<?xml version="1.0" encoding="UTF-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<rewrite^>
echo       ^<rules^>
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^>
echo           ^<match url="(.*)" /^>
echo           ^<action type="Rewrite" url="http://localhost:8000/{R:1}" /^>
echo         ^</rule^>
echo       ^</rules^>
echo     ^</rewrite^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\inetpub\wwwroot\hexmes\web.config"

if exist "C:\inetpub\wwwroot\hexmes\web.config" (
    echo %GREEN%Reverse proxy configuration created!%RESET%
    echo.
    echo %CYAN%HexMES should now be accessible at:%RESET%
    echo %BRIGHT_GREEN%https://gr-ot-igngw-02p.mes.proterra.com/hexmes%RESET%
    echo.
    echo %YELLOW%Note: Make sure URL Rewrite module is installed in IIS%RESET%
) else (
    echo %RED%Failed to create reverse proxy configuration%RESET%
)

pause
goto MAIN_MENU

:UNINSTALL_SERVICE
cls
echo %CYAN%Uninstall HexMES Service%RESET%
echo.
echo %RED%This will completely remove the HexMES service%RESET%
set /p confirm=%YELLOW%Are you sure? (y/N): %RESET%

if /i not "%confirm%"=="y" goto MAIN_MENU

echo %YELLOW%Stopping and removing service...%RESET%
nssm stop "HexMES Dashboard"
timeout /t 2 >nul
nssm remove "HexMES Dashboard" confirm

echo %GREEN%Service removed successfully%RESET%
pause
goto MAIN_MENU

:EXIT
echo.
echo %CYAN%Thank you for using HexMES Dashboard Installer!%RESET%
echo %GRAY%For support, contact the Proterra Manufacturing team%RESET%
echo.
pause
exit /b 0