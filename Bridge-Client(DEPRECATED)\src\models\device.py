import serial
import logging
from datetime import datetime

# Set up log
logging.basicConfig(level=logging.INFO)


class Device:
    def __init__(self, com_port, hioki_number):
        self.com_port = com_port
        self.hioki_number = hioki_number
        self.serial_connection = None
        self.is_connected = False
        self.last_reading = None
        self.last_reading_time = None
        self.testing = False
        self.logger = logging.getLogger(__name__)

    def connect(self):
        """Connect to the device"""
        try:
            self.serial_connection = serial.Serial(
                port=self.com_port,
                baudrate=9600,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1,
            )
            self.is_connected = True
            self.logger.info(f"Connected to device on {self.com_port}")
        except Exception as e:
            self.logger.error(
                f"Failed to connect to device on {self.com_port}: {str(e)}"
            )
            self.is_connected = False
            raise

    def disconnect(self):
        """Disconnect from the device"""
        if self.serial_connection and self.is_connected:
            try:
                self.serial_connection.close()
                self.is_connected = False
                self.logger.info(f"Disconnected from device on {self.com_port}")
            except Exception as e:
                self.logger.error(f"Error disconnecting from device: {str(e)}")

    def read_measurement(self):
        """Read a measurement from the device"""
        if not self.is_connected and not self.testing:
            self.logger.warning(
                f"Cannot read from disconnected device on {self.com_port}"
            )
            return None

        try:
            if self.serial_connection.in_waiting:
                raw_data = self.serial_connection.readline().decode("utf-8")
                formatted_value = self.format_reading(raw_data)
                if formatted_value:
                    self.last_reading = formatted_value
                    self.last_reading_time = datetime.now()
                return formatted_value
        except Exception as e:
            raise IOError(f"Error reading from device: {str(e)}")

        return None

    def format_reading(self, raw_data):
        """Format the raw reading from the device"""
        try:
            # Remove any non-numeric characters except decimal point
            cleaned = "".join(c for c in raw_data if c.isdigit() or c == ".")
            if cleaned:
                return float(cleaned)
        except Exception as e:
            self.logger.error(f"Error formatting reading: {str(e)}")
        return None

    def get_mqtt_topic(self):
        """Get the MQTT topic for this device"""
        return f"nomuda/gvl/tools/GVB-Hioki-RM3545/{self.hioki_number}/result/impedance"

    def to_dict(self):
        """Convert device to dictionary for JSON serialization"""
        return {
            "port": self.com_port,
            "hioki_id": self.hioki_number,
            "connected": self.is_connected,
            "last_reading": self.last_reading,
            "last_reading_time": (
                self.last_reading_time.isoformat() if self.last_reading_time else None
            ),
        }

    def __repr__(self):
        return f"Device(com_port='{self.com_port}', hioki_number='{self.hioki_number}', connected={self.is_connected})"
