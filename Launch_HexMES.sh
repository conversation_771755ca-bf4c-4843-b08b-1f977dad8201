#!/bin/bash
VERSION="0.2.6"

main_menu() {
    clear
    echo "+-------------------------------------------------+"
    if command -v figlet >/dev/null 2>&1; then
        figlet -f standard "HexMES"
    else
        echo "HexMES"
    fi
    echo "+-------------------------------------------------+"

    echo "Version: $VERSION"
    echo
    check_python
    echo "-------------------------------------------------"
    echo "1. Install and setup Python"
    echo "2. Install dependencies (all_requirements.txt)"
    echo "3. Start HexMES Bridge"
    echo "4. Start HexMES Wireless Admin"
    echo "5. Exit"
    echo
    read -rp "Select an option: " choice
    case $choice in
        1) install_python ;;
        2) install_requirements ;;
        3) run_bridge ;;
        4) run_dashboard ;;
        5) exit 0 ;;
        *) main_menu ;;
    esac
}

check_python() {
    if command -v python3 >/dev/null 2>&1; then
        PY_STATUS="Active"
    else
        PY_STATUS="Not installed"
    fi
    echo "Python: $PY_STATUS"
}

install_python() {
    clear
    if command -v python3 >/dev/null 2>&1; then
        echo "Python already installed."
        read -n1 -r -p "Press any key to continue..." && main_menu
    fi
    if command -v apt-get >/dev/null 2>&1; then
        echo "Installing Python using apt..."
        sudo apt-get update && sudo apt-get install -y python3 python3-venv
    else
        echo "Please install Python manually from https://www.python.org/downloads/"
    fi
    read -n1 -r -p "Press any key to continue..." && main_menu
}

install_requirements() {
    clear
    if [ "$PY_STATUS" = "Not installed" ]; then
        echo "Python is not installed. Please install Python first."
        read -n1 -r -p "Press any key to continue..." && main_menu
    fi
    python3 -m pip install --upgrade pip
    pip3 install -r all_requirements.txt
    if [ $? -eq 0 ]; then
        echo "Dependencies installed successfully."
    else
        echo "Failed to install dependencies. Ensure pip is configured correctly."
    fi
    read -n1 -r -p "Press any key to continue..." && main_menu
}

run_bridge() {
    clear
    if [ "$PY_STATUS" = "Not installed" ]; then
        echo "Python is not installed. Please install Python first."
        read -n1 -r -p "Press any key to continue..." && main_menu
    fi
    cd "$(dirname "$0")/Bridge-Client" || return
    if [ ! -d venv ]; then
        python3 -m venv venv || { echo "Failed to create virtual environment."; read -n1 -r -p "Press any key to continue..."; cd ..; main_menu; }
    fi
    source venv/bin/activate
    pip install -r ../all_requirements.txt
    echo "Starting HexMES Bridge..."
    python run.py
    echo "HexMES Bridge has stopped."
    deactivate
    cd ..
    main_menu
}

run_dashboard() {
    clear
    if [ "$PY_STATUS" = "Not installed" ]; then
        echo "Python is not installed. Please install Python first."
        read -n1 -r -p "Press any key to continue..." && main_menu
    fi
    cd "$(dirname "$0")/Dashboard" || return
    if [ ! -d venv ]; then
        python3 -m venv venv || { echo "Failed to create virtual environment."; read -n1 -r -p "Press any key to continue..."; cd ..; main_menu; }
    fi
    source venv/bin/activate
    pip install -r ../all_requirements.txt
    echo "Starting HexMES Wireless Admin..."
    python run_prod.py
    echo "HexMES Wireless Admin has stopped."
    deactivate
    cd ..
    main_menu
}

main_menu
