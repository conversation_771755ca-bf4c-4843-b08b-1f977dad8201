import json
import logging
from typing import Dict, Any, Optional
from .result_processors import ResultProcessorFactory

logger = logging.getLogger(__name__)


class CommandHandler:
    """Handles commands received from VF via MQTT."""
    
    def __init__(self, db_service, device_manager):
        self.db_service = db_service
        self.device_manager = device_manager
    
    def process_command(self, topic: str, payload: str) -> bool:
        """Process a command received via MQTT.
        
        Args:
            topic: MQTT topic the command was received on
            payload: JSON payload containing the command
            
        Returns:
            bool: True if command was processed successfully
        """
        try:
            # Parse the command payload
            command_data = json.loads(payload) if isinstance(payload, str) else payload
            
            # Extract device information from topic
            device_info = self._extract_device_from_topic(topic)
            if not device_info:
                logger.error(f"Could not extract device info from topic: {topic}")
                return False
            
            device_id = device_info["device_id"]
            entity_type = device_info["entity_type"]
            
            logger.info(f"Processing command for {entity_type} device {device_id}: {command_data.get('toolCommand')}")
            
            # Store the command in MESDB for linking to future results
            command_id = ResultProcessorFactory.store_command(entity_type, device_id, command_data)
            
            if command_id:
                logger.info(f"Stored command {command_id} for device {device_id}")
            else:
                logger.warning(f"Failed to store command for device {device_id}")
            
            # Send the actual command to the device if needed
            self._send_command_to_device(device_id, entity_type, command_data)
            
            return True
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in command payload: {payload[:100]}... Error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error processing command: {e}")
            return False
    
    def _extract_device_from_topic(self, topic: str) -> Optional[Dict[str, str]]:
        """Extract device information from MQTT topic.
        
        Expected topic formats:
        - nomuda/gvl/tools/GVB-Hioki-RM3545/{device_id}/command
        - nomuda/gvl/tools/GVB-CTS-Manifold/{device_id}/command
        - nomuda/gvl/tools/GVB-CTS-Enclosure/{device_id}/command
        - nomuda/gvl/tools/GVB-BRANSON/{device_id}/command
        """
        try:
            parts = topic.split('/')
            if len(parts) < 5:
                return None
            
            tool_type = parts[3]  # e.g., "GVB-Hioki-RM3545"
            device_id = parts[4]
            
            # Map tool types to entity types
            if "Hioki" in tool_type:
                entity_type = "hioki"
            elif "CTS" in tool_type:
                entity_type = "cts"
            elif "BRANSON" in tool_type:
                entity_type = "ultrasonic"
            else:
                entity_type = "custom"
            
            return {
                "device_id": device_id,
                "entity_type": entity_type,
                "tool_type": tool_type
            }
            
        except Exception as e:
            logger.error(f"Error extracting device from topic {topic}: {e}")
            return None
    
    def _send_command_to_device(self, device_id: str, entity_type: str, command_data: Dict[str, Any]):
        """Send the actual command to the physical device if needed."""
        try:
            # For Hioki devices, we might need to send specific commands
            if entity_type == "hioki":
                # Find the device in the database
                device = self.db_service.get_device_by_hioki_id(device_id)
                if device and self.device_manager:
                    # Send command via device manager
                    tool_command = command_data.get("toolCommand", "")
                    if tool_command:
                        result = self.device_manager.send_command_to_device(
                            device["id"], tool_command
                        )
                        logger.info(f"Sent command to Hioki device {device_id}: {result}")
            
            # For CTS devices, commands are typically just for context
            elif entity_type == "cts":
                logger.info(f"CTS command received for {device_id} - stored for context linking")
            
            # For ultrasonic welders, commands might trigger specific actions
            elif entity_type == "ultrasonic":
                logger.info(f"Ultrasonic command received for {device_id} - stored for context linking")
                
        except Exception as e:
            logger.error(f"Error sending command to device {device_id}: {e}")


def setup_command_handler(mqtt_service, db_service, device_manager) -> CommandHandler:
    """Set up the command handler and subscribe to command topics."""
    handler = CommandHandler(db_service, device_manager)
    
    # Subscribe to command topics
    command_topics = [
        "nomuda/gvl/tools/+/+/command",  # Wildcard for all tool command topics
    ]
    
    for topic in command_topics:
        mqtt_service.subscribe_to_topic(topic, handler.process_command)
        logger.info(f"Subscribed to command topic: {topic}")
    
    return handler
