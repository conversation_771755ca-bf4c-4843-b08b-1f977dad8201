from Dashboard.src.app import (
    create_app,
    start_background_tasks,
    get_local_ip,
    socketio,
)

app = create_app()

if __name__ == "__main__":
    print("Starting Dashboard in production mode...")
    local_ip = get_local_ip()
    print(f"Access the dashboard at http://{local_ip}:8000")
    start_background_tasks()
    try:
        socketio.run(app, host="0.0.0.0", port=8000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error starting application: {e}")
