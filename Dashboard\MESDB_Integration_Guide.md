# MESDB Integration Guide

This guide explains how the Dashboard integrates with the MESDB (Manufacturing Execution System Database) to store commands and results for data team reporting.

## Overview

The MESDB integration provides:
- **Command Storage**: All commands from VF are stored with full context
- **Result Linking**: Results are automatically linked to the most recent command for each device
- **Data Team Access**: Structured data for reporting and analysis
- **Traceability**: Complete audit trail of commands and results

## Architecture

### Data Flow
1. **Command Received**: VF sends command via MQTT → Dashboard stores in MESDB
2. **Result Generated**: Device produces result → Dashboard processes and links to last command
3. **Data Available**: Data team can query linked command-result pairs

### Command-Result Linking Strategy
- **Hioki/Ultrasonic**: Results linked to last command until new command received
- **CTS**: Results include context in data array, but also linked to last command
- **Multiple Commands**: If multiple commands sent before result, links to most recent
- **Command Gaps**: Large time gaps between command/result still maintain linkage

## Database Schema

### ToolData Table
The main table stores both commands and results:

```sql
CREATE TABLE ToolData (
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    
    -- Device Information
    DeviceID NVARCHAR(50) NOT NULL,           -- Device identifier
    DeviceType NVARCHAR(20) NOT NULL,         -- 'hioki', 'cts', 'ultrasonic'
    
    -- Record Type and Linkage
    RecordType NVARCHAR(10) NOT NULL,         -- 'COMMAND' or 'RESULT'
    CommandID NVARCHAR(100),                  -- Unique command ID
    LinkedCommandID NVARCHAR(100),            -- Links results to commands
    
    -- VF Context Fields
    WorkStationName NVARCHAR(100),
    PersonName NVARCHAR(100),
    TestItemName NVARCHAR(200),
    ActivityID NVARCHAR(50),
    TaskName NVARCHAR(200),
    OptionCode NVARCHAR(100),
    LotID NVARCHAR(50),
    UnitID NVARCHAR(50),
    WorkOrderNumber NVARCHAR(100),
    
    -- Result Data
    ResultValue NVARCHAR(100),               -- Primary measurement
    ResultUnit NVARCHAR(20),                 -- Unit of measurement
    ResultStatus NVARCHAR(20),               -- 'PASS', 'FAIL', 'ERROR'
    
    -- Timestamps and Raw Data
    ReceivedTimestamp DATETIME2 DEFAULT GETUTCDATE(),
    RawData NVARCHAR(MAX),
    ResultData NVARCHAR(MAX)                 -- Full JSON result
);
```

## Setup Instructions

### 1. Install Dependencies
```bash
pip install pyodbc
```

### 2. Install ODBC Driver
- **Windows**: Download "ODBC Driver 17 for SQL Server" from Microsoft
- **Linux**: Follow Microsoft's installation guide for your distribution

### 3. Configure Connection
Copy `mesdb_config_template.env` to `.env` and update with your MESDB details:
```env
MESDB_CONNECTION_STRING=DRIVER={ODBC Driver 17 for SQL Server};SERVER=your-server;DATABASE=MESDB;UID=username;PWD=password
```

### 4. Create Database Table
Run the SQL script on your MESDB server:
```bash
sqlcmd -S your-server -d MESDB -i sql/create_mesdb_table.sql
```

## Usage Examples

### Query Command-Result Pairs
```sql
-- Get all results with their linked commands
SELECT 
    c.WorkStationName,
    c.PersonName,
    c.TestItemName,
    c.UnitID,
    c.CommandTimestamp,
    r.ResultValue,
    r.ResultStatus,
    r.ResultTimestamp
FROM CommandResultView
WHERE c.DeviceType = 'hioki'
    AND c.CommandTimestamp >= '2024-01-01'
ORDER BY c.CommandTimestamp DESC;
```

### Find Unlinked Results
```sql
-- Results without commands (potential issues)
SELECT DeviceID, ResultValue, ReceivedTimestamp
FROM ToolData 
WHERE RecordType = 'RESULT' 
    AND LinkedCommandID IS NULL;
```

### Performance Metrics
```sql
-- Average time between command and result
SELECT 
    DeviceType,
    AVG(TimeBetweenCommandAndResult) as AvgResponseTime,
    COUNT(*) as ResultCount
FROM CommandResultView
WHERE ResultID IS NOT NULL
GROUP BY DeviceType;
```

## Device-Specific Behavior

### Hioki Devices
- **Commands**: Store impedance test parameters
- **Results**: Impedance values in milliohms
- **Linking**: Each result links to most recent command

### CTS Testers  
- **Commands**: Store leak test parameters
- **Results**: Pressure loss and test pressure values
- **Linking**: Results link to recent command + include context in data array

### Ultrasonic Welders
- **Commands**: Store weld parameters and part information
- **Results**: Weld success/failure with energy, power, time measurements
- **Linking**: Each weld result links to most recent command

## Monitoring and Troubleshooting

### Check Connection
The Dashboard logs MESDB connection status:
```
INFO: Stored command ABC123 for device H001 in MESDB
ERROR: Failed to connect to MESDB: [connection error details]
```

### Verify Data Flow
1. Send test command via VF
2. Check ToolData table for COMMAND record
3. Generate result from device
4. Verify RESULT record with LinkedCommandID

### Common Issues
- **Connection Failures**: Check ODBC driver installation and connection string
- **Missing Links**: Verify command storage before result generation
- **Performance**: Ensure indexes are created for large datasets

## Data Team Queries

The `CommandResultView` provides an easy interface for reporting:
- All commands with their linked results
- Timing information between commands and results
- Complete context from VF for traceability
- Device performance metrics
